#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip>
#include <random>

// 包含SpInfer头文件
#include "SpInfer/csrc/SpMM_API.cu"

// 包含Sputnik头文件
#include "SpInfer/third_party/sputnik/sputnik/spmm/cuda_spmm.h"

// 错误检查宏
#define CHECK_CUDA(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__ << " - " << cudaGetErrorString(err) << std::endl; \
        exit(1); \
    } \
} while(0)

class HybridSputnikTest {
private:
    int M, N, K;
    float sparsity;
    
    // 原始稀疏矩阵数据 (SpInfer格式)
    std::vector<half> A_values_spinfer;
    std::vector<int> A_indices_spinfer;
    std::vector<int> A_offsets_spinfer;
    
    // Matrix2数据 (转换为Sputnik CSR格式)
    std::vector<float> A2_values_csr;
    std::vector<int> A2_row_offsets_csr;
    std::vector<int> A2_column_indices_csr;
    std::vector<int> A2_row_indices_csr;
    
    // 密集矩阵和输出
    std::vector<half> B_dense;
    std::vector<half> C1_output;  // Matrix1 (TC) 输出
    std::vector<float> C2_output; // Matrix2 (Sputnik) 输出
    std::vector<half> C_final;    // 最终合并输出
    
    // GPU数据
    half *A_values_gpu, *B_dense_gpu, *C1_output_gpu, *C_final_gpu;
    int *A_indices_gpu, *A_offsets_gpu;
    
    // Sputnik GPU数据
    float *A2_values_gpu, *C2_output_gpu;
    int *A2_row_offsets_gpu, *A2_column_indices_gpu, *A2_row_indices_gpu;
    float *B_dense_float_gpu;  // B矩阵的float版本
    
    int nonzeros_total, nonzeros_matrix2;
    cudaEvent_t start, stop;
    cudaStream_t stream1, stream2;  // 双流并行执行
    
public:
    HybridSputnikTest(int m, int n, int k, float sparsity_ratio = 0.9f) 
        : M(m), N(n), K(k), sparsity(sparsity_ratio) {
        
        // 创建CUDA事件和流
        CHECK_CUDA(cudaEventCreate(&start));
        CHECK_CUDA(cudaEventCreate(&stop));
        CHECK_CUDA(cudaStreamCreateWithFlags(&stream1, cudaStreamNonBlocking));
        CHECK_CUDA(cudaStreamCreateWithFlags(&stream2, cudaStreamNonBlocking));
        
        std::cout << "=== 混合Kernel测试 (SpInfer TC + Sputnik CC) ===" << std::endl;
        std::cout << "矩阵大小: A(" << M << "x" << K << ") × B(" << K << "x" << N << ") = C(" << M << "x" << N << ")" << std::endl;
        std::cout << "目标稀疏度: " << sparsity * 100 << "%" << std::endl;
        
        generateHybridMatrix();
        allocateMemory();
        copyToGPU();
    }
    
    ~HybridSputnikTest() {
        // 清理GPU内存
        if (A_values_gpu) cudaFree(A_values_gpu);
        if (A_indices_gpu) cudaFree(A_indices_gpu);
        if (A_offsets_gpu) cudaFree(A_offsets_gpu);
        if (B_dense_gpu) cudaFree(B_dense_gpu);
        if (C1_output_gpu) cudaFree(C1_output_gpu);
        if (C_final_gpu) cudaFree(C_final_gpu);
        
        if (A2_values_gpu) cudaFree(A2_values_gpu);
        if (A2_row_offsets_gpu) cudaFree(A2_row_offsets_gpu);
        if (A2_column_indices_gpu) cudaFree(A2_column_indices_gpu);
        if (A2_row_indices_gpu) cudaFree(A2_row_indices_gpu);
        if (B_dense_float_gpu) cudaFree(B_dense_float_gpu);
        if (C2_output_gpu) cudaFree(C2_output_gpu);
        
        cudaEventDestroy(start);
        cudaEventDestroy(stop);
        cudaStreamDestroy(stream1);
        cudaStreamDestroy(stream2);
    }
    
private:
    void generateHybridMatrix() {
        std::random_device rd;
        std::mt19937 gen(42);
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);
        std::uniform_real_distribution<float> val_dis(-1.0f, 1.0f);
        
        std::cout << "生成混合稀疏矩阵..." << std::endl;
        
        // 生成Matrix1数据 (前40%行，块稀疏，适合TC)
        int matrix1_rows = M * 0.4;  // 40%的行
        A_offsets_spinfer.resize(matrix1_rows + 1);
        A_offsets_spinfer[0] = 0;
        
        for (int i = 0; i < matrix1_rows; i++) {
            // 块稀疏模式：每16个元素为一块，40%的块非零
            for (int block = 0; block < K / 16; block++) {
                if (dis(gen) < 0.4) {  // 40%的块非零
                    for (int j = 0; j < 16; j++) {
                        int col = block * 16 + j;
                        A_indices_spinfer.push_back(col);
                        A_values_spinfer.push_back(__float2half(val_dis(gen)));
                    }
                }
            }
            A_offsets_spinfer[i + 1] = A_values_spinfer.size();
        }
        
        // 生成Matrix2数据 (后10%行，稀疏块，适合CC/Sputnik)
        int matrix2_start_row = M * 0.9;  // 从90%开始
        int matrix2_rows = M - matrix2_start_row;  // 10%的行
        
        A2_row_offsets_csr.resize(matrix2_rows + 1);
        A2_row_offsets_csr[0] = 0;
        
        for (int i = 0; i < matrix2_rows; i++) {
            // 稀疏块模式：每16个元素为一块，10%的块内部25%稀疏
            for (int block = 0; block < K / 16; block++) {
                if (dis(gen) < 0.1) {  // 10%的块非零
                    for (int j = 0; j < 16; j++) {
                        if (dis(gen) < 0.25) {  // 块内25%稀疏
                            int col = block * 16 + j;
                            A2_column_indices_csr.push_back(col);
                            A2_values_csr.push_back(val_dis(gen));
                        }
                    }
                }
            }
            A2_row_offsets_csr[i + 1] = A2_values_csr.size();
        }
        
        nonzeros_total = A_values_spinfer.size();
        nonzeros_matrix2 = A2_values_csr.size();
        
        // 生成行索引重排序 (Sputnik需要)
        A2_row_indices_csr.resize(matrix2_rows);
        for (int i = 0; i < matrix2_rows; i++) {
            A2_row_indices_csr[i] = i;
        }
        
        // 生成密集矩阵B
        B_dense.resize(K * N);
        for (int i = 0; i < K * N; i++) {
            B_dense[i] = __float2half(val_dis(gen));
        }
        
        // 初始化输出矩阵
        C1_output.resize(M * N, __float2half(0.0f));
        C2_output.resize(matrix2_rows * N, 0.0f);
        C_final.resize(M * N, __float2half(0.0f));
        
        std::cout << "混合矩阵生成完成:" << std::endl;
        std::cout << "  Matrix1 (TC): " << matrix1_rows << " 行, " << nonzeros_total << " 非零元素" << std::endl;
        std::cout << "  Matrix2 (Sputnik): " << matrix2_rows << " 行, " << nonzeros_matrix2 << " 非零元素" << std::endl;
    }
    
    void allocateMemory() {
        std::cout << "分配GPU内存..." << std::endl;
        
        // SpInfer数据
        CHECK_CUDA(cudaMalloc(&A_values_gpu, nonzeros_total * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&A_indices_gpu, nonzeros_total * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&A_offsets_gpu, A_offsets_spinfer.size() * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&B_dense_gpu, K * N * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&C1_output_gpu, M * N * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&C_final_gpu, M * N * sizeof(half)));
        
        // Sputnik数据
        CHECK_CUDA(cudaMalloc(&A2_values_gpu, nonzeros_matrix2 * sizeof(float)));
        CHECK_CUDA(cudaMalloc(&A2_row_offsets_gpu, A2_row_offsets_csr.size() * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&A2_column_indices_gpu, nonzeros_matrix2 * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&A2_row_indices_gpu, A2_row_indices_csr.size() * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&B_dense_float_gpu, K * N * sizeof(float)));
        CHECK_CUDA(cudaMalloc(&C2_output_gpu, A2_row_indices_csr.size() * N * sizeof(float)));
        
        std::cout << "GPU内存分配完成" << std::endl;
    }
    
    void copyToGPU() {
        std::cout << "拷贝数据到GPU..." << std::endl;
        
        // 拷贝SpInfer数据
        CHECK_CUDA(cudaMemcpy(A_values_gpu, A_values_spinfer.data(), nonzeros_total * sizeof(half), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A_indices_gpu, A_indices_spinfer.data(), nonzeros_total * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A_offsets_gpu, A_offsets_spinfer.data(), A_offsets_spinfer.size() * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(B_dense_gpu, B_dense.data(), K * N * sizeof(half), cudaMemcpyHostToDevice));
        
        // 拷贝Sputnik数据
        CHECK_CUDA(cudaMemcpy(A2_values_gpu, A2_values_csr.data(), nonzeros_matrix2 * sizeof(float), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A2_row_offsets_gpu, A2_row_offsets_csr.data(), A2_row_offsets_csr.size() * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A2_column_indices_gpu, A2_column_indices_csr.data(), nonzeros_matrix2 * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A2_row_indices_gpu, A2_row_indices_csr.data(), A2_row_indices_csr.size() * sizeof(int), cudaMemcpyHostToDevice));
        
        // 转换B矩阵为float格式 (Sputnik需要)
        convertHalfToFloat();
        
        std::cout << "数据拷贝完成" << std::endl;
    }
    
    void convertHalfToFloat() {
        // 在GPU上转换half到float
        std::vector<float> B_dense_float(K * N);
        for (int i = 0; i < K * N; i++) {
            B_dense_float[i] = __half2float(B_dense[i]);
        }
        CHECK_CUDA(cudaMemcpy(B_dense_float_gpu, B_dense_float.data(), K * N * sizeof(float), cudaMemcpyHostToDevice));
    }

public:
    void runHybridTest() {
        std::cout << "\n=== 开始混合Kernel功能测试 ===" << std::endl;

        // 清零输出
        CHECK_CUDA(cudaMemset(C1_output_gpu, 0, M * N * sizeof(half)));
        CHECK_CUDA(cudaMemset(C2_output_gpu, 0, A2_row_indices_csr.size() * N * sizeof(float)));
        CHECK_CUDA(cudaMemset(C_final_gpu, 0, M * N * sizeof(half)));

        // 并行执行两个kernel
        std::cout << "启动Matrix1 (TC) kernel..." << std::endl;
        // 这里应该调用SpInfer的Matrix1 kernel
        // SpMM_API_Matrix1(..., stream1);

        std::cout << "启动Matrix2 (Sputnik CC) kernel..." << std::endl;
        cudaError_t sputnik_result = sputnik::CudaSpmm(
            A2_row_indices_csr.size(), K, N, nonzeros_matrix2,
            A2_row_indices_gpu,
            A2_values_gpu,
            A2_row_offsets_gpu,
            A2_column_indices_gpu,
            B_dense_float_gpu,
            C2_output_gpu,
            stream2
        );

        if (sputnik_result != cudaSuccess) {
            std::cerr << "Sputnik SPMM 调用失败: " << cudaGetErrorString(sputnik_result) << std::endl;
            return;
        }

        // 同步两个流
        CHECK_CUDA(cudaStreamSynchronize(stream1));
        CHECK_CUDA(cudaStreamSynchronize(stream2));

        // 合并结果 (这里需要实现结果合并kernel)
        std::cout << "合并计算结果..." << std::endl;
        // mergeResults<<<...>>>(C1_output_gpu, C2_output_gpu, C_final_gpu, ...);

        std::cout << "混合Kernel测试完成" << std::endl;
    }

    void runBenchmark() {
        std::cout << "\n=== 开始混合Kernel性能测试 ===" << std::endl;

        const int warm_up_iterations = 10;
        const int benchmark_iterations = 100;

        // 预热
        std::cout << "预热中..." << std::endl;
        for (int i = 0; i < warm_up_iterations; i++) {
            // SpMM_API_Matrix1(..., stream1);
            sputnik::CudaSpmm(A2_row_indices_csr.size(), K, N, nonzeros_matrix2,
                             A2_row_indices_gpu, A2_values_gpu, A2_row_offsets_gpu,
                             A2_column_indices_gpu, B_dense_float_gpu, C2_output_gpu, stream2);
            CHECK_CUDA(cudaStreamSynchronize(stream1));
            CHECK_CUDA(cudaStreamSynchronize(stream2));
        }

        // 性能测试
        std::cout << "开始性能测试 (" << benchmark_iterations << " 次迭代)..." << std::endl;
        CHECK_CUDA(cudaEventRecord(start));

        for (int i = 0; i < benchmark_iterations; i++) {
            // 并行执行
            // SpMM_API_Matrix1(..., stream1);
            sputnik::CudaSpmm(A2_row_indices_csr.size(), K, N, nonzeros_matrix2,
                             A2_row_indices_gpu, A2_values_gpu, A2_row_offsets_gpu,
                             A2_column_indices_gpu, B_dense_float_gpu, C2_output_gpu, stream2);
        }

        CHECK_CUDA(cudaStreamSynchronize(stream1));
        CHECK_CUDA(cudaStreamSynchronize(stream2));
        CHECK_CUDA(cudaEventRecord(stop));
        CHECK_CUDA(cudaEventSynchronize(stop));

        // 计算性能
        float elapsed_time;
        CHECK_CUDA(cudaEventElapsedTime(&elapsed_time, start, stop));

        float avg_time = elapsed_time / benchmark_iterations;
        double total_flops = 2.0 * (nonzeros_total + nonzeros_matrix2) * N;
        double gflops = (total_flops / (avg_time * 1e-3)) / 1e9;

        printResults(avg_time, gflops);
    }

private:
    void printResults(float time_ms, double gflops) {
        std::cout << "\n========== 混合Kernel性能测试结果 ==========" << std::endl;
        std::cout << "矩阵大小: " << M << " x " << K << " x " << N << std::endl;
        std::cout << "Matrix1 (TC): " << nonzeros_total << " 非零元素" << std::endl;
        std::cout << "Matrix2 (Sputnik): " << nonzeros_matrix2 << " 非零元素" << std::endl;
        std::cout << "总非零元素: " << (nonzeros_total + nonzeros_matrix2) << " / " << (M * K) << std::endl;
        std::cout << "平均时间: " << std::fixed << std::setprecision(3) << time_ms << " ms" << std::endl;
        std::cout << "总性能: " << std::fixed << std::setprecision(2) << gflops << " GFLOPS" << std::endl;
        std::cout << "=============================================" << std::endl;
    }
};

int main(int argc, char* argv[]) {
    // 解析命令行参数
    int M = 18944, N = 3584, K = 32;  // 与SpInfer一致
    float sparsity = 0.9f;

    if (argc >= 4) {
        M = std::atoi(argv[1]);
        N = std::atoi(argv[2]);
        K = std::atoi(argv[3]);
    }
    if (argc >= 5) {
        sparsity = std::atof(argv[4]);
    }

    std::cout << "混合Kernel测试程序 (SpInfer TC + Sputnik CC)" << std::endl;
    std::cout << "使用参数: M=" << M << ", N=" << N << ", K=" << K << ", 稀疏度=" << sparsity << std::endl;

    try {
        HybridSputnikTest test(M, N, K, sparsity);

        test.runHybridTest();
        test.runBenchmark();

    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
