#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <cuda_fp16.h>
#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip>

// 错误检查宏
#define CHECK_CUDA(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__ << " - " << cudaGetErrorString(err) << std::endl; \
        exit(1); \
    } \
} while(0)

#define CHECK_CUBLAS(call) do { \
    cublasStatus_t status = call; \
    if (status != CUBLAS_STATUS_SUCCESS) { \
        std::cerr << "cuBLAS error at " << __FILE__ << ":" << __LINE__ << " - " << status << std::endl; \
        exit(1); \
    } \
} while(0)

class CublasGemmBenchmark {
private:
    int M, N, K;
    half *A_gpu, *B_gpu, *C_gpu;
    half *A_cpu, *B_cpu, *C_cpu;
    cublasHandle_t cublas_handle;
    cudaEvent_t start, stop;
    
    // 性能计算参数
    static constexpr int WARM_UP_ITERATIONS = 10;
    static constexpr int BENCHMARK_ITERATIONS = 100;
    
public:
    CublasGemmBenchmark(int m, int n, int k) : M(m), N(n), K(k) {
        // 初始化cuBLAS
        CHECK_CUBLAS(cublasCreate(&cublas_handle));
        
        // 设置数学模式为Tensor Core
        CHECK_CUBLAS(cublasSetMathMode(cublas_handle, CUBLAS_TENSOR_OP_MATH));
        
        // 创建CUDA事件
        CHECK_CUDA(cudaEventCreate(&start));
        CHECK_CUDA(cudaEventCreate(&stop));
        
        // 分配内存
        allocateMemory();
        
        // 初始化数据
        initializeData();
        
        std::cout << "cuBLAS GEMM Benchmark 初始化完成" << std::endl;
        std::cout << "矩阵大小: A(" << M << "x" << K << ") × B(" << K << "x" << N << ") = C(" << M << "x" << N << ")" << std::endl;
        std::cout << "使用 Tensor Core 加速" << std::endl;
    }
    
    ~CublasGemmBenchmark() {
        // 清理资源
        if (A_gpu) cudaFree(A_gpu);
        if (B_gpu) cudaFree(B_gpu);
        if (C_gpu) cudaFree(C_gpu);
        if (A_cpu) delete[] A_cpu;
        if (B_cpu) delete[] B_cpu;
        if (C_cpu) delete[] C_cpu;
        
        cudaEventDestroy(start);
        cudaEventDestroy(stop);
        cublasDestroy(cublas_handle);
    }
    
private:
    void allocateMemory() {
        // GPU内存分配
        CHECK_CUDA(cudaMalloc(&A_gpu, M * K * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&B_gpu, K * N * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&C_gpu, M * N * sizeof(half)));
        
        // CPU内存分配
        A_cpu = new half[M * K];
        B_cpu = new half[K * N];
        C_cpu = new half[M * N];
        
        std::cout << "内存分配完成:" << std::endl;
        std::cout << "  A: " << (M * K * sizeof(half)) / (1024*1024) << " MB" << std::endl;
        std::cout << "  B: " << (K * N * sizeof(half)) / (1024*1024) << " MB" << std::endl;
        std::cout << "  C: " << (M * N * sizeof(half)) / (1024*1024) << " MB" << std::endl;
    }
    
    void initializeData() {
        // 初始化矩阵A (M x K)
        for (int i = 0; i < M * K; i++) {
            A_cpu[i] = __float2half(static_cast<float>(rand()) / RAND_MAX * 2.0f - 1.0f);
        }
        
        // 初始化矩阵B (K x N) 
        for (int i = 0; i < K * N; i++) {
            B_cpu[i] = __float2half(static_cast<float>(rand()) / RAND_MAX * 2.0f - 1.0f);
        }
        
        // 初始化矩阵C (M x N)
        for (int i = 0; i < M * N; i++) {
            C_cpu[i] = __float2half(0.0f);
        }
        
        // 拷贝到GPU
        CHECK_CUDA(cudaMemcpy(A_gpu, A_cpu, M * K * sizeof(half), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(B_gpu, B_cpu, K * N * sizeof(half), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(C_gpu, C_cpu, M * N * sizeof(half), cudaMemcpyHostToDevice));
        
        std::cout << "数据初始化完成" << std::endl;
    }
    
public:
    void runBenchmark() {
        std::cout << "\n开始 cuBLAS GEMM 性能测试..." << std::endl;
        
        // 设置GEMM参数
        const half alpha = __float2half(1.0f);
        const half beta = __float2half(0.0f);
        
        // 预热
        std::cout << "预热中..." << std::endl;
        for (int i = 0; i < WARM_UP_ITERATIONS; i++) {
            CHECK_CUBLAS(cublasHgemm(cublas_handle,
                                   CUBLAS_OP_N, CUBLAS_OP_N,
                                   N, M, K,
                                   &alpha,
                                   B_gpu, N,  // B是 K x N，leading dimension是N
                                   A_gpu, K,  // A是 M x K，leading dimension是K  
                                   &beta,
                                   C_gpu, N)); // C是 M x N，leading dimension是N
        }
        CHECK_CUDA(cudaDeviceSynchronize());
        
        // 性能测试
        std::cout << "开始性能测试..." << std::endl;
        CHECK_CUDA(cudaEventRecord(start));
        
        for (int i = 0; i < BENCHMARK_ITERATIONS; i++) {
            CHECK_CUBLAS(cublasHgemm(cublas_handle,
                                   CUBLAS_OP_N, CUBLAS_OP_N,
                                   N, M, K,
                                   &alpha,
                                   B_gpu, N,
                                   A_gpu, K,
                                   &beta,
                                   C_gpu, N));
        }
        
        CHECK_CUDA(cudaEventRecord(stop));
        CHECK_CUDA(cudaEventSynchronize(stop));
        
        // 计算性能
        float elapsed_time;
        CHECK_CUDA(cudaEventElapsedTime(&elapsed_time, start, stop));
        
        float avg_time = elapsed_time / BENCHMARK_ITERATIONS;
        double flops = 2.0 * M * N * K;  // GEMM的FLOP数
        double tflops = (flops / (avg_time * 1e-3)) / 1e12;  // TFLOPS
        
        // 输出结果
        printResults(avg_time, tflops);
    }
    
private:
    void printResults(float time_ms, double tflops) {
        std::cout << "\n========== cuBLAS GEMM 性能测试结果 ==========" << std::endl;
        std::cout << "矩阵大小: " << M << " x " << K << " x " << N << std::endl;
        std::cout << "迭代次数: " << BENCHMARK_ITERATIONS << std::endl;
        std::cout << "平均时间: " << std::fixed << std::setprecision(3) << time_ms << " ms" << std::endl;
        std::cout << "性能:     " << std::fixed << std::setprecision(2) << tflops << " TFLOPS" << std::endl;
        
        // 计算内存带宽
        double memory_bytes = (static_cast<double>(M * K) + K * N + M * N) * sizeof(half);
        double bandwidth_gb_s = (memory_bytes / (time_ms * 1e-3)) / 1e9;
        std::cout << "内存带宽: " << std::fixed << std::setprecision(2) << bandwidth_gb_s << " GB/s" << std::endl;
        std::cout << "=============================================" << std::endl;
    }
};

int main(int argc, char* argv[]) {
    // 解析命令行参数
    int M = 18944, N = 3584, K = 32;  // 默认值与SpInfer一致
    
    if (argc >= 4) {
        M = std::atoi(argv[1]);
        N = std::atoi(argv[2]);
        K = std::atoi(argv[3]);
    }
    
    std::cout << "cuBLAS GEMM Tensor Core 基准测试" << std::endl;
    std::cout << "使用矩阵大小: " << M << " x " << N << " x " << K << std::endl;
    
    try {
        // 创建并运行基准测试
        CublasGemmBenchmark benchmark(M, N, K);
        benchmark.runBenchmark();
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
