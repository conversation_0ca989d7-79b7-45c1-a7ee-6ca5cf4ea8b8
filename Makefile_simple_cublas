# 简单的 cuBLAS GEMM 测试 Makefile

# 编译器设置
NVCC = /usr/local/cuda/bin/nvcc
CXX = g++

# CUDA架构设置
CUDA_ARCH = -gencode arch=compute_86,code=sm_86

# 编译标志
NVCC_FLAGS = -ccbin $(CXX) \
             -I/usr/local/cuda/include/ \
             -m64 \
             -Xcompiler -fPIC \
             --threads 0 \
             --std=c++11 \
             -maxrregcount=255 \
             --use_fast_math \
             --ptxas-options=-v,-warn-lmem-usage,--warn-on-spills \
             $(CUDA_ARCH)

# 链接库
LIBS = -lcublas -lcudart

# 目标文件
TARGET = simple_cublas_test
SOURCE = simple_cublas_test.cu

# 默认目标
all: $(TARGET)

# 编译规则
$(TARGET): $(SOURCE)
	@echo "编译简单 cuBLAS 测试..."
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE) $(LIBS)
	@echo "编译完成: $(TARGET)"

# 运行测试 (默认参数: 18944 x 3584 x 32)
run: $(TARGET)
	@echo "运行 cuBLAS 测试 (默认参数)..."
	./$(TARGET)

# 运行测试 (自定义参数)
run_custom: $(TARGET)
	@echo "运行 cuBLAS 测试 (M=$(M) N=$(N) K=$(K))..."
	./$(TARGET) $(M) $(N) $(K)

# 清理
clean:
	@echo "清理编译文件..."
	rm -f $(TARGET)

# 帮助信息
help:
	@echo "简单 cuBLAS GEMM 测试 Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all        - 编译程序"
	@echo "  run        - 运行测试 (默认参数: 18944 x 3584 x 32)"
	@echo "  run_custom - 运行测试 (自定义参数: make run_custom M=<m> N=<n> K=<k>)"
	@echo "  clean      - 清理编译文件"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make run                              # 默认参数"
	@echo "  make run_custom M=8192 N=4096 K=64   # 自定义参数"
	@echo "  CUDA_VISIBLE_DEVICES=2 make run      # 指定GPU"

# 声明伪目标
.PHONY: all run run_custom clean help
