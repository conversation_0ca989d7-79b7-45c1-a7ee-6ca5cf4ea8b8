/*
 * 基于NVIDIA官方示例的cuSPARSE BELL格式测试程序
 * 验证BELL格式的正确性和数据布局
 */
#include <cuda_fp16.h>        // data types
#include <cuda_runtime_api.h> // cudaMalloc, cudaMemcpy, etc.
#include <cusparse.h>         // cusparseSpMM
#include <cstdio>            // printf
#include <cstdlib>           // EXIT_FAILURE
#include <cmath>             // fabs

#define CHECK_CUDA(func)                                                       \
{                                                                              \
    cudaError_t status = (func);                                               \
    if (status != cudaSuccess) {                                               \
        std::printf("CUDA API failed at line %d with error: %s (%d)\n",        \
               __LINE__, cudaGetErrorString(status), status);                  \
        return EXIT_FAILURE;                                                   \
    }                                                                          \
}

#define CHECK_CUSPARSE(func)                                                   \
{                                                                              \
    cusparseStatus_t status = (func);                                          \
    if (status != CUSPARSE_STATUS_SUCCESS) {                                   \
        std::printf("CUSPARSE API failed at line %d with error: %s (%d)\n",    \
               __LINE__, cusparseGetErrorString(status), status);              \
        return EXIT_FAILURE;                                                   \
    }                                                                          \
}

const int EXIT_UNSUPPORTED = 2;

int main() {
    printf("=== NVIDIA官方BELL格式示例测试 ===\n");
    
    // Host problem definition (NVIDIA官方示例)
    int   A_num_rows      = 4;
    int   A_num_cols      = 4;
    int   A_ell_blocksize = 2;
    int   A_ell_cols      = 2;
    int   A_num_blocks    = A_ell_cols * A_num_rows /
                           (A_ell_blocksize * A_ell_blocksize);
    int   B_num_rows      = A_num_cols;
    int   B_num_cols      = 3;
    int   ldb             = B_num_rows;
    int   ldc             = A_num_rows;
    int   B_size          = ldb * B_num_cols;
    int   C_size          = ldc * B_num_cols;
    
    printf("矩阵维度: A(%d x %d) × B(%d x %d) = C(%d x %d)\n", 
           A_num_rows, A_num_cols, B_num_rows, B_num_cols, A_num_rows, B_num_cols);
    printf("BELL格式: 块大小=%d, ellCols=%d, 总块数=%d\n", 
           A_ell_blocksize, A_ell_cols, A_num_blocks);
    
    // NVIDIA官方示例数据
    int hA_columns[]    = { 1, 0};  // 块列索引
    __half hA_values[]    = { 1.0f, 2.0f, 3.0f, 4.0f,    // 第一个块 (列主序)
                              5.0f, 6.0f, 7.0f, 8.0f};   // 第二个块 (列主序)
    __half hB[]           = { 1.0f,  2.0f,  3.0f,  4.0f,  // 列主序存储
                              5.0f,  6.0f,  7.0f,  8.0f,
                              9.0f, 10.0f, 11.0f, 12.0f };
    __half hC[]           = { 0.0f, 0.0f, 0.0f, 0.0f,
                              0.0f, 0.0f, 0.0f, 0.0f,
                              0.0f, 0.0f, 0.0f, 0.0f };
    __half hC_result[]    = { 11.0f, 25.0f,  17.0f,  23.0f,  // 期望结果
                              23.0f, 53.0f,  61.0f,  83.0f,
                              35.0f, 81.0f, 105.0f, 143.0f };
    float alpha           = 1.0f;
    float beta            = 0.0f;
    
    printf("\n数据布局验证:\n");
    printf("A块列索引: [%d, %d]\n", hA_columns[0], hA_columns[1]);
    printf("A块值 (列主序): [%.1f, %.1f, %.1f, %.1f, %.1f, %.1f, %.1f, %.1f]\n",
           __half2float(hA_values[0]), __half2float(hA_values[1]), 
           __half2float(hA_values[2]), __half2float(hA_values[3]),
           __half2float(hA_values[4]), __half2float(hA_values[5]), 
           __half2float(hA_values[6]), __half2float(hA_values[7]));
    
    //--------------------------------------------------------------------------
    // Check compute capability
    cudaDeviceProp props;
    CHECK_CUDA( cudaGetDeviceProperties(&props, 0) )
    if (props.major < 7) {
      std::printf("cusparseSpMM with blocked ELL format is supported only "
                  "with compute capability at least 7.0\n");
      return EXIT_UNSUPPORTED;
    }
    printf("GPU: %s (计算能力: %d.%d)\n", props.name, props.major, props.minor);
    
    //--------------------------------------------------------------------------
    // Device memory management
    int    *dA_columns;
    __half *dA_values, *dB, *dC;
    CHECK_CUDA( cudaMalloc((void**) &dA_columns, A_num_blocks * sizeof(int)) )
    CHECK_CUDA( cudaMalloc((void**) &dA_values,
                                    A_ell_cols * A_num_rows * sizeof(__half)) )
    CHECK_CUDA( cudaMalloc((void**) &dB, B_size * sizeof(__half)) )
    CHECK_CUDA( cudaMalloc((void**) &dC, C_size * sizeof(__half)) )

    CHECK_CUDA( cudaMemcpy(dA_columns, hA_columns,
                           A_num_blocks * sizeof(int),
                           cudaMemcpyHostToDevice) )
    CHECK_CUDA( cudaMemcpy(dA_values, hA_values,
                           A_ell_cols * A_num_rows * sizeof(__half),
                           cudaMemcpyHostToDevice) )
    CHECK_CUDA( cudaMemcpy(dB, hB, B_size * sizeof(__half),
                           cudaMemcpyHostToDevice) )
    CHECK_CUDA( cudaMemcpy(dC, hC, C_size * sizeof(__half),
                           cudaMemcpyHostToDevice) )
    
    printf("\nGPU内存分配完成\n");
    
    //--------------------------------------------------------------------------
    // CUSPARSE APIs
    cusparseHandle_t     handle = NULL;
    cusparseSpMatDescr_t matA;
    cusparseDnMatDescr_t matB, matC;
    void*                dBuffer    = NULL;
    size_t               bufferSize = 0;
    CHECK_CUSPARSE( cusparseCreate(&handle) )
    
    // Create sparse matrix A in blocked ELL format
    CHECK_CUSPARSE( cusparseCreateBlockedEll(
                                      &matA,
                                      A_num_rows, A_num_cols, A_ell_blocksize,
                                      A_ell_cols, dA_columns, dA_values,
                                      CUSPARSE_INDEX_32I,
                                      CUSPARSE_INDEX_BASE_ZERO, CUDA_R_16F) )
    // Create dense matrix B (列主序)
    CHECK_CUSPARSE( cusparseCreateDnMat(&matB, A_num_cols, B_num_cols, ldb, dB,
                                        CUDA_R_16F, CUSPARSE_ORDER_COL) )
    // Create dense matrix C (列主序)
    CHECK_CUSPARSE( cusparseCreateDnMat(&matC, A_num_rows, B_num_cols, ldc, dC,
                                        CUDA_R_16F, CUSPARSE_ORDER_COL) )
    
    // allocate an external buffer if needed
    CHECK_CUSPARSE( cusparseSpMM_bufferSize(
                                 handle,
                                 CUSPARSE_OPERATION_NON_TRANSPOSE,
                                 CUSPARSE_OPERATION_NON_TRANSPOSE,
                                 &alpha, matA, matB, &beta, matC, CUDA_R_32F,
                                 CUSPARSE_SPMM_ALG_DEFAULT, &bufferSize) )
    CHECK_CUDA( cudaMalloc(&dBuffer, bufferSize) )
    
    printf("cuSPARSE BELL初始化完成，缓冲区大小: %zu bytes\n", bufferSize);

    // execute SpMM
    printf("\n执行cuSPARSE BELL SpMM...\n");
    CHECK_CUSPARSE( cusparseSpMM(handle,
                                 CUSPARSE_OPERATION_NON_TRANSPOSE,
                                 CUSPARSE_OPERATION_NON_TRANSPOSE,
                                 &alpha, matA, matB, &beta, matC, CUDA_R_32F,
                                 CUSPARSE_SPMM_ALG_DEFAULT, dBuffer) )

    // destroy matrix/vector descriptors
    CHECK_CUSPARSE( cusparseDestroySpMat(matA) )
    CHECK_CUSPARSE( cusparseDestroyDnMat(matB) )
    CHECK_CUSPARSE( cusparseDestroyDnMat(matC) )
    CHECK_CUSPARSE( cusparseDestroy(handle) )
    
    //--------------------------------------------------------------------------
    // device result check
    CHECK_CUDA( cudaMemcpy(hC, dC, C_size * sizeof(__half),
                           cudaMemcpyDeviceToHost) )
    
    printf("\n结果验证:\n");
    printf("计算结果:\n");
    for (int j = 0; j < B_num_cols; j++) {
        printf("列 %d: [", j);
        for (int i = 0; i < A_num_rows; i++) {
            printf("%.1f", __half2float(hC[i + j * ldc]));
            if (i < A_num_rows - 1) printf(", ");
        }
        printf("]\n");
    }
    
    printf("期望结果:\n");
    for (int j = 0; j < B_num_cols; j++) {
        printf("列 %d: [", j);
        for (int i = 0; i < A_num_rows; i++) {
            printf("%.1f", __half2float(hC_result[i + j * ldc]));
            if (i < A_num_rows - 1) printf(", ");
        }
        printf("]\n");
    }
    
    int correct = 1;
    double max_error = 0.0;
    for (int i = 0; i < A_num_rows; i++) {
        for (int j = 0; j < B_num_cols; j++) {
            float c_value  = __half2float(hC[i + j * ldc]);
            float c_result = __half2float(hC_result[i + j * ldc]);
            double error = fabs(c_value - c_result);
            if (error > max_error) max_error = error;
            if (error > 1e-3) {  // 允许小的数值误差
                correct = 0;
                printf("误差在 [%d,%d]: 计算值=%.6f, 期望值=%.6f, 误差=%.6f\n", 
                       i, j, c_value, c_result, error);
            }
        }
    }
    
    printf("\n最大误差: %.6e\n", max_error);
    if (correct)
        std::printf("✓ BELL格式测试通过!\n");
    else
        std::printf("✗ BELL格式测试失败: 结果不匹配\n");
    
    //--------------------------------------------------------------------------
    // device memory deallocation
    CHECK_CUDA( cudaFree(dBuffer) )
    CHECK_CUDA( cudaFree(dA_columns) )
    CHECK_CUDA( cudaFree(dA_values) )
    CHECK_CUDA( cudaFree(dB) )
    CHECK_CUDA( cudaFree(dC) )
    
    printf("\n=== NVIDIA BELL格式示例测试完成 ===\n");
    return correct ? EXIT_SUCCESS : EXIT_FAILURE;
}
