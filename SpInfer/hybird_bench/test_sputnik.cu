/***************************************************************************
 * Sputnik测试程序 - 高稀疏度矩阵专用
 * 基于sputnik库实现90%稀疏度的稀疏矩阵乘法，作为Matrix2 CC kernel的替代方案
 ***************************************************************************/
#include <iostream>
#include <vector>
#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <cusparse_v2.h>
#include <cmath>
#include <cstring>

// 先包含sputnik相关头文件
#include "../kernel_benchmark/sputnik_utils.h"
#include "sputnik/sputnik.h"

// 性能测试常量
#define WARM_UP_ITERATION 3
#define BENCHMARK_ITERATION 10

// 错误检查宏（使用不同的名称避免冲突）
#define CHECK_CUDA_ERROR(call) \
    do { \
        cudaError_t error = call; \
        if (error != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(error)); \
            exit(1); \
        } \
    } while(0)

// 性能对比工具函数
void PrintSputnikPerformance(const char* KernelName, float milliseconds, float tflops, double error = 0.0) {
    printf("%-15s -> Time: %6.3f ms, Performance: %6.2f TFLOPS",
           KernelName, milliseconds, tflops);
    if (error > 0.0) {
        printf(", Error: %.2e", error);
    }
    printf("\n");
}

double ComputeSputnikError(half* reference, half* result, int M, int N) {
    double totalError = 0.0;
    for (int i = 0; i < M * N; i++) {
        double diff = __half2float(reference[i]) - __half2float(result[i]);
        totalError += diff * diff;
    }
    return sqrt(totalError);
}

// 从文件加载稠密矩阵
bool load_dense_matrix_from_file(const char* filename, half* A_h, int M, int K) {
    FILE* file = fopen(filename, "rb");
    if (!file) {
        printf("无法打开文件: %s\n", filename);
        return false;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    size_t expected_size = M * K * sizeof(half);
    size_t file_elements = file_size / sizeof(half);

    if (file_size != expected_size) {
        printf("文件大小不匹配: 期望 %zu 字节 (%d x %d), 实际 %ld 字节 (%zu 元素)\n",
               expected_size, M, K, file_size, file_elements);
        fclose(file);
        return false;
    }

    size_t elements_read = fread(A_h, sizeof(half), M * K, file);
    fclose(file);

    if (elements_read != M * K) {
        printf("文件读取错误: 期望 %d 个元素，实际读取 %zu 个\n", M * K, elements_read);
        return false;
    }

    printf("成功从文件 %s 加载 %d x %d 矩阵\n", filename, M, K);
    return true;
}

// 生成自定义稀疏矩阵（90%稀疏度）
void generate_custom_sparse_matrix(half* A_h, int M, int K, float sparsity_ratio) {
    printf("生成自定义稀疏矩阵: M=%d, K=%d, 稀疏度=%.2f\n", M, K, sparsity_ratio);

    for (int i = 0; i < M; i++) {
        for (int j = 0; j < K; j++) {
            float rand_val = static_cast<float>(rand()) / RAND_MAX;
            if (rand_val > sparsity_ratio) {
                // 非零元素：生成[-1, 1]范围的随机值
                float val = (static_cast<float>(rand()) / RAND_MAX) * 2.0f - 1.0f;
                A_h[i * K + j] = __float2half(val);
            } else {
                // 零元素
                A_h[i * K + j] = __float2half(0.0f);
            }
        }
    }
}

// 生成稠密矩阵B
void generate_dense_matrix_B(half* B_h, int K, int N) {
    printf("生成稠密矩阵B: K=%d, N=%d\n", K, N);

    for (int i = 0; i < K * N; i++) {
        float val = (static_cast<float>(rand()) / RAND_MAX) * 2.0f - 1.0f;
        B_h[i] = __float2half(val);
    }
}

// 转置矩阵B (column-major -> row-major)
void transpose_matrix_B(half* B_col_major, half* B_row_major, int K, int N) {
    for (int i = 0; i < K; i++) {
        for (int j = 0; j < N; j++) {
            B_row_major[i * N + j] = B_col_major[i + j * K];
        }
    }
}

int main(int argc, char** argv) {
    // 参数设置 (Qwen2.5 FFN第一层)
    int M_GLOBAL = 18944;  // FFN权重矩阵行数
    int K_GLOBAL = 3584;   // FFN权重矩阵列数/输入维度
    int N_GLOBAL = 32;     // sequence length
    bool use_file = false;  // 默认使用随机数据
    float sparsity_ratio = 0.9f;  // 90%稀疏度

    if (argc >= 4) {
        M_GLOBAL = atoi(argv[1]);
        K_GLOBAL = atoi(argv[2]);
        N_GLOBAL = atoi(argv[3]);
        if (argc >= 5 && strcmp(argv[4], "file") == 0) {
            use_file = true;
        }
        if (argc >= 6) {
            sparsity_ratio = atof(argv[5]);
        }
    }

    printf("=== Sputnik测试程序（高稀疏度优化）===\n");
    printf("矩阵维度: A(%d x %d) × B(%d x %d) = C(%d x %d)\n",
           M_GLOBAL, K_GLOBAL, K_GLOBAL, N_GLOBAL, M_GLOBAL, N_GLOBAL);
    if (use_file) {
        printf("数据源: ../Input_date/three_matrices_latest/matrix2_sparse_block.bin\n");
    } else {
        printf("数据源: 随机生成（%.0f%%稀疏度）\n", sparsity_ratio * 100);
    }

    // ========== 步骤1: 准备输入数据 ==========
    printf("\n步骤1: 准备输入数据\n");

    // 分配host内存
    half* A_h = (half*)malloc(sizeof(half) * M_GLOBAL * K_GLOBAL);
    half* B_h = (half*)malloc(sizeof(half) * K_GLOBAL * N_GLOBAL);
    half* B_transposed_h = (half*)malloc(sizeof(half) * K_GLOBAL * N_GLOBAL);

    if (!A_h || !B_h || !B_transposed_h) {
        printf("Host内存分配失败\n");
        return -1;
    }

    if (use_file) {
        // 从文件加载矩阵2（稀疏块FFN权重）
        if (!load_dense_matrix_from_file("../Input_date/three_matrices_latest/matrix2_sparse_block.bin",
                                        A_h, M_GLOBAL, K_GLOBAL)) {
            printf("加载矩阵2失败，改用随机生成\n");
            generate_custom_sparse_matrix(A_h, M_GLOBAL, K_GLOBAL, sparsity_ratio);
        }
    } else {
        // 生成自定义稀疏矩阵A（FFN权重，90%稀疏的稀疏块）
        generate_custom_sparse_matrix(A_h, M_GLOBAL, K_GLOBAL, sparsity_ratio);
    }

    // 生成稠密矩阵B（输入激活，sequence_length=32）
    generate_dense_matrix_B(B_h, K_GLOBAL, N_GLOBAL);
    
    // 转置矩阵B为row-major格式（sputnik要求）
    transpose_matrix_B(B_h, B_transposed_h, K_GLOBAL, N_GLOBAL);

    // ========== 步骤2: Sputnik格式转换 ==========
    printf("\n步骤2: Sputnik格式转换\n");

    // 转换half矩阵A为float（sputnik要求）
    float* A_float_h = (float*)malloc(sizeof(float) * M_GLOBAL * K_GLOBAL);
    for (int i = 0; i < M_GLOBAL * K_GLOBAL; i++) {
        A_float_h[i] = __half2float(A_h[i]);
    }

    // 创建sputnik稀疏矩阵对象
    sputnik_utils::SparseMatrix sparse_matrix(M_GLOBAL, K_GLOBAL, A_float_h, sputnik_utils::IDENTITY, 4);
    sputnik_utils::CudaSparseMatrix<half2> sparse_matrix_gpu(sparse_matrix);

    printf("Sputnik格式转换完成: %d个非零元素\n", sparse_matrix_gpu.NumElementsWithPadding());

    // ========== 步骤3: 数据传输到GPU ==========
    printf("\n步骤3: 数据传输到GPU\n");

    // 分配GPU内存
    half* A_gpu, *B_gpu, *B_transposed_gpu, *C_gpu;
    CHECK_CUDA_ERROR(cudaMalloc(&A_gpu, sizeof(half) * M_GLOBAL * K_GLOBAL));
    CHECK_CUDA_ERROR(cudaMalloc(&B_gpu, sizeof(half) * K_GLOBAL * N_GLOBAL));
    CHECK_CUDA_ERROR(cudaMalloc(&B_transposed_gpu, sizeof(half) * K_GLOBAL * N_GLOBAL));
    CHECK_CUDA_ERROR(cudaMalloc(&C_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL));

    // 拷贝数据到GPU
    CHECK_CUDA_ERROR(cudaMemcpy(A_gpu, A_h, sizeof(half) * M_GLOBAL * K_GLOBAL, cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(B_gpu, B_h, sizeof(half) * K_GLOBAL * N_GLOBAL, cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(B_transposed_gpu, B_transposed_h, sizeof(half) * K_GLOBAL * N_GLOBAL, cudaMemcpyHostToDevice));

    printf("数据传输完成\n");

    // ========== 步骤4: 多库性能对比测试 ==========
    printf("\n步骤4: 多库性能对比测试\n");

    // 创建CUDA事件用于计时
    cudaEvent_t start, stop;
    CHECK_CUDA_ERROR(cudaEventCreate(&start));
    CHECK_CUDA_ERROR(cudaEventCreate(&stop));

    // 计算性能指标的基础数据
    long long ops = 2LL * M_GLOBAL * N_GLOBAL * K_GLOBAL;  // 乘加操作数

    printf("\n=== 性能对比结果 ===\n");

    // 1. cuBLAS (Tensor Core) 性能测试
    printf("测试 cuBLAS (Tensor Core)...\n");
    half* C_cublas_gpu = NULL;
    CHECK_CUDA_ERROR(cudaMalloc(&C_cublas_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL));
    CHECK_CUDA_ERROR(cudaMemset(C_cublas_gpu, 0, sizeof(half) * M_GLOBAL * N_GLOBAL));

    cublasHandle_t handle;
    cublasCreate(&handle);
    cublasSetStream(handle, 0);
    cublasSetMathMode(handle, CUBLAS_DEFAULT_MATH);  // 启用Tensor Core

    const float alpha = 1.0f, beta = 0.0f;

    // cuBLAS预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        cublasGemmEx(handle, CUBLAS_OP_T, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, A_gpu, CUDA_R_16F, K_GLOBAL,
                     B_gpu, CUDA_R_16F, K_GLOBAL,
                     &beta, C_cublas_gpu, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0));
    }
    CHECK_CUDA_ERROR(cudaDeviceSynchronize());

    // cuBLAS计时
    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        cublasGemmEx(handle, CUBLAS_OP_T, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, A_gpu, CUDA_R_16F, K_GLOBAL,
                     B_gpu, CUDA_R_16F, K_GLOBAL,
                     &beta, C_cublas_gpu, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0));
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float milliseconds_cublas = 0;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&milliseconds_cublas, start, stop));
    milliseconds_cublas /= BENCHMARK_ITERATION;
    float tflops_cublas = (ops / (milliseconds_cublas / 1000.0)) / 1e12;

    // 2. Sputnik 性能测试
    printf("测试 Sputnik (高稀疏度优化)...\n");
    half* C_sputnik_gpu = NULL;
    CHECK_CUDA_ERROR(cudaMalloc(&C_sputnik_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL));
    CHECK_CUDA_ERROR(cudaMemset(C_sputnik_gpu, 0, sizeof(half) * M_GLOBAL * N_GLOBAL));

    // Sputnik预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        CUDA_CALL(sputnik::CudaSpmm(M_GLOBAL,
                                    K_GLOBAL,
                                    N_GLOBAL,
                                    sparse_matrix_gpu.NumElementsWithPadding(),
                                    sparse_matrix_gpu.RowIndices(),
                                    sparse_matrix_gpu.Values(),
                                    sparse_matrix_gpu.RowOffsets(),
                                    sparse_matrix_gpu.ColumnIndices(),
                                    reinterpret_cast<half2*>(B_transposed_gpu),
                                    reinterpret_cast<half2*>(C_sputnik_gpu),
                                    0));
    }
    CHECK_CUDA_ERROR(cudaDeviceSynchronize());

    // Sputnik计时
    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        CUDA_CALL(sputnik::CudaSpmm(M_GLOBAL,
                                    K_GLOBAL,
                                    N_GLOBAL,
                                    sparse_matrix_gpu.NumElementsWithPadding(),
                                    sparse_matrix_gpu.RowIndices(),
                                    sparse_matrix_gpu.Values(),
                                    sparse_matrix_gpu.RowOffsets(),
                                    sparse_matrix_gpu.ColumnIndices(),
                                    reinterpret_cast<half2*>(B_transposed_gpu),
                                    reinterpret_cast<half2*>(C_sputnik_gpu),
                                    0));
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float milliseconds_sputnik = 0;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&milliseconds_sputnik, start, stop));
    milliseconds_sputnik /= BENCHMARK_ITERATION;
    float tflops_sputnik = (ops / (milliseconds_sputnik / 1000.0)) / 1e12;

    // ========== 步骤5: 结果验证和性能对比 ==========
    printf("\n步骤5: 结果验证和性能对比\n");

    // 将结果拷贝回host进行验证
    half* C_sputnik_h = (half*)malloc(sizeof(half) * M_GLOBAL * N_GLOBAL);
    half* C_cublas_h = (half*)malloc(sizeof(half) * M_GLOBAL * N_GLOBAL);

    CHECK_CUDA_ERROR(cudaMemcpy(C_sputnik_h, C_sputnik_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL, cudaMemcpyDeviceToHost));
    CHECK_CUDA_ERROR(cudaMemcpy(C_cublas_h, C_cublas_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL, cudaMemcpyDeviceToHost));

    // 计算误差
    double error_sputnik = ComputeSputnikError(C_cublas_h, C_sputnik_h, M_GLOBAL, N_GLOBAL);

    // 输出性能对比结果
    printf("\n=== Sputnik性能对比总结 ===\n");
    PrintSputnikPerformance("cuBLAS (TC)", milliseconds_cublas, tflops_cublas);
    PrintSputnikPerformance("Sputnik", milliseconds_sputnik, tflops_sputnik, error_sputnik);

    // 计算加速比
    float speedup = milliseconds_cublas / milliseconds_sputnik;
    printf("\nSputnik vs cuBLAS 加速比: %.2fx\n", speedup);

    // 简单验证：检查结果是否包含非零值
    int non_zero_count = 0;
    for (int i = 0; i < M_GLOBAL * N_GLOBAL; i++) {
        if (__half2float(C_sputnik_h[i]) != 0.0f) {
            non_zero_count++;
        }
    }
    printf("Sputnik结果矩阵中非零元素数量: %d / %d\n", non_zero_count, M_GLOBAL * N_GLOBAL);

    // 清理cuBLAS资源
    cublasDestroy(handle);
    cudaFree(C_cublas_gpu);
    free(C_cublas_h);

    // ========== 清理资源 ==========
    printf("\n清理资源\n");

    // 释放host内存
    free(A_h);
    free(B_h);
    free(B_transposed_h);
    free(A_float_h);
    free(C_sputnik_h);

    // 释放GPU内存
    cudaFree(A_gpu);
    cudaFree(B_gpu);
    cudaFree(B_transposed_gpu);
    cudaFree(C_gpu);
    cudaFree(C_sputnik_gpu);

    cudaEventDestroy(start);
    cudaEventDestroy(stop);

    printf("Sputnik测试程序执行完成!\n");
    return 0;
}
