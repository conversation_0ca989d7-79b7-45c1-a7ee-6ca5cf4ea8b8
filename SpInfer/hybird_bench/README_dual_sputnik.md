# TC+Sputnik混合并行测试程序

## 📋 概述

`test_dual_sputnik.cu` 是一个创新的混合并行测试程序，实现了Matrix1_TC和Sputnik的并行执行架构。该程序验证了不同稀疏度优化策略的混合使用效果。

## 🏗️ 架构设计

### 混合内核架构
- **内核1**: Matrix1_TC (块稀疏，60%稀疏度，Tensor Core优化)
- **内核2**: Sputnik (高稀疏度，90%稀疏度，CUDA Core优化)
- **并行方式**: CUDA Streams非阻塞并行执行

### 技术特点
- **格式转换**: SpInfer bitmap格式 + Sputnik CSR格式
- **数据类型**: half精度 + float精度混合处理
- **内存优化**: 独立的GPU内存空间和数据流
- **性能分析**: 完整的并行效率和加速比分析

## 🚀 编译和运行

### 编译
```bash
cd SpInfer
make -f Makefile_three_kernels test_dual_sputnik
```

### 运行
```bash
# 直接运行
make -f Makefile_three_kernels run_dual_sputnik

# 或者手动运行
LD_LIBRARY_PATH=./third_party/glog/build/lib:./third_party/sputnik/build/sputnik:$LD_LIBRARY_PATH ./test_dual_sputnik
```

## 📊 测试流程

### 步骤1: 数据准备
- 生成60%稀疏度的Matrix1数据
- 生成90%稀疏度的Matrix2数据
- 创建对应的稠密矩阵B

### 步骤2: 混合稀疏格式转换
- **Matrix1**: SpInfer bitmap格式转换
- **Matrix2**: Sputnik CSR格式转换
- **数据类型**: half↔float转换处理

### 步骤3-4: GPU内存管理
- 独立的GPU内存分配
- 优化的数据传输策略

### 步骤5: CUDA Streams创建
- 非阻塞Stream并行执行
- 精确的事件计时机制

### 步骤6: 单独性能测试
- Matrix1_TC独立性能测试
- Sputnik独立性能测试
- 与cuBLAS基准对比

### 步骤7: 混合并行执行
- TC+Sputnik同时执行
- Stream重叠效率分析

### 步骤8: 顺序执行对比
- 顺序执行基准测试
- 并行加速比计算

### 步骤9: cuBLAS基准测试
- 完整的基准性能对比
- TFLOPS性能计算

### 步骤10: 性能结果分析
- 详细的性能对比报告
- 并行效率分析

### 步骤11: 结果验证
- 数值精度验证
- 输出矩阵统计分析

## 📈 预期输出

```
=== TC+Sputnik混合并行测试程序 ===
矩阵维度: A(18944 x 3584) × B(3584 x 32) = C(18944 x 32)
内核1: Matrix1_TC (块稀疏，60%稀疏度，Tensor Core优化)
内核2: Sputnik (高稀疏度，90%稀疏度，CUDA Core优化)
并行方式: CUDA Streams非阻塞并行执行

=== 单独性能对比 ===
内核1 (Matrix1_TC):
  cuBLAS 基准      -> 时间:  X.XXX ms, 性能:  XX.XX TFLOPS
  SpInfer Matrix1  -> 时间:  X.XXX ms, 性能:  XX.XX TFLOPS, 加速比: X.XXx

内核2 (Sputnik):
  cuBLAS 基准      -> 时间:  X.XXX ms, 性能:  XX.XX TFLOPS
  Sputnik 内核     -> 时间:  X.XXX ms, 性能:  XX.XX TFLOPS, 加速比: X.XXx

=== 混合并行性能分析 ===
理论最佳时间 (max(TC,Sputnik)): X.XXX ms
TC+Sputnik 实际并行执行: X.XXX ms, 性能: XX.XX TFLOPS
TC+Sputnik 顺序执行: X.XXX ms, 性能: XX.XX TFLOPS
并行效率: XX.X% (实际/理想)
并行加速比: X.XXx (顺序/并行)

=== 整体性能对比 ===
cuBLAS 基准性能: X.XXX ms (单个矩阵乘法)
TC+Sputnik 顺序执行: X.XXX ms (两个矩阵乘法)
TC+Sputnik 并行执行: X.XXX ms (两个矩阵乘法)
平均单矩阵加速比 (TC+Sputnik顺序 vs cuBLAS): X.XXx
平均单矩阵加速比 (TC+Sputnik并行 vs cuBLAS): X.XXx

=== 结果验证 ===
内核1 (Matrix1_TC):
  相对误差: X.XXe-XX
内核2 (Sputnik):
  相对误差: X.XXe-XX

TC+Sputnik混合并行执行测试完成！
阶段3完成：TC+Sputnik混合架构实现成功！
```

## 🔧 技术细节

### 数据格式转换
- **Matrix1**: 保持SpInfer的bitmap压缩格式
- **Matrix2**: 转换为Sputnik的CSR格式
- **类型转换**: half精度与float精度的无缝转换

### 并行优化策略
- **Split_K**: 设置为2以减少资源竞争
- **Stream管理**: 非阻塞Stream确保真正的并行执行
- **内存优化**: 独立的GPU内存空间避免数据竞争

### 性能分析指标
- **TFLOPS**: 浮点运算性能
- **并行效率**: 实际并行时间与理论最优时间的比值
- **加速比**: 顺序执行与并行执行的时间比值
- **相对误差**: 与cuBLAS基准的数值精度对比

## 🎯 应用场景

### 适用情况
- **混合稀疏度**: 同时处理中等稀疏度(60%)和高稀疏度(90%)矩阵
- **异构优化**: 需要同时利用Tensor Core和CUDA Core的场景
- **并行加速**: 需要最大化GPU利用率的应用
- **性能对比**: 评估不同稀疏矩阵库的性能差异

### 技术验证
- **架构可行性**: 验证TC+Sputnik混合架构的实用性
- **性能优势**: 对比传统单一内核的性能提升
- **并行效率**: 评估Stream并行的实际效果
- **数值稳定性**: 验证混合精度计算的准确性

## 📝 注意事项

1. **依赖库**: 确保sputnik和glog库正确编译和链接
2. **GPU架构**: 需要支持Tensor Core的GPU (Compute Capability >= 7.0)
3. **内存需求**: 大矩阵需要足够的GPU内存
4. **精度转换**: 注意half和float之间的精度损失

## 🔗 相关文件

- `test_dual_kernels.cu`: 原始的TC+CC双内核测试
- `test_sputnik.cu`: 单独的Sputnik性能测试
- `SpMM_Kernel_Matrix1.cuh`: Matrix1_TC内核实现
- `sputnik_utils.h`: Sputnik工具函数
- `Makefile_three_kernels`: 编译配置文件
