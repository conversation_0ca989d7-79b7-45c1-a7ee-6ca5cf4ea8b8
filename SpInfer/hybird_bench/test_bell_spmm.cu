/***************************************************************************
 * cuSPARSE BELL格式SPMM性能测试程序
 * 测试Blocked-ELL格式的Tensor Core加速稀疏矩阵乘法
 * 
 * 基于NVIDIA官方博客和cuSPARSE文档实现
 * https://developer.nvidia.com/blog/accelerating-matrix-multiplication-with-block-sparse-format-and-nvidia-tensor-cores/
 ***************************************************************************/
#include <iostream>
#include <vector>
#include <cmath>
#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <cusparse.h>
#include <nvtx3/nvToolsExt.h>

// 性能测试常量
#define WARM_UP_ITERATION 5
#define BENCHMARK_ITERATION 10

// 错误检查宏
#define CHECK_CUDA(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        exit(1); \
    } \
} while(0)

#define CHECK_CUSPARSE(call) do { \
    cusparseStatus_t st = call; \
    if (st != CUSPARSE_STATUS_SUCCESS) { \
        printf("cuSPARSE error at %s:%d - %d\n", __FILE__, __LINE__, (int)st); \
        exit(1); \
    } \
} while(0)

#define CHECK_CUBLAS(call) do { \
    cublasStatus_t st = call; \
    if (st != CUBLAS_STATUS_SUCCESS) { \
        printf("cuBLAS error at %s:%d - %d\n", __FILE__, __LINE__, (int)st); \
        exit(1); \
    } \
} while(0)

// 性能对比工具函数
void PrintPerformance(const char* KernelName, float milliseconds, float tflops, double error = 0.0) {
    printf("%-20s -> Time: %6.3f ms, Performance: %6.2f TFLOPS",
           KernelName, milliseconds, tflops);
    if (error > 0.0) {
        printf(", Error: %.2e", error);
    }
    printf("\n");
}

double ComputeError(half* reference, half* result, int M, int N) {
    double totalError = 0.0;
    for (int i = 0; i < M * N; i++) {
        double diff = __half2float(reference[i]) - __half2float(result[i]);
        totalError += diff * diff;
    }
    return sqrt(totalError / (M * N));
}

// 生成块稀疏矩阵 (BELL格式)
void generateBlockSparseMatrix(half* matrix, int M, int K, int blockSize, float sparsity, 
                              std::vector<int>& blockColIndices, std::vector<half>& blockValues) {
    int mb = (M + blockSize - 1) / blockSize;
    int kb = (K + blockSize - 1) / blockSize;
    int maxBlocksPerRow = (int)((1.0f - sparsity) * kb) + 1;  // 估算每行最大块数

    // cuSPARSE BELL格式要求：ellCols必须是blockSize的倍数
    maxBlocksPerRow = ((maxBlocksPerRow + blockSize - 1) / blockSize) * blockSize;
    
    printf("Matrix blocks: %d x %d, Block size: %d x %d, Max blocks per row: %d\n", 
           mb, kb, blockSize, blockSize, maxBlocksPerRow);
    
    // 初始化矩阵为零
    memset(matrix, 0, M * K * sizeof(half));
    
    // 初始化BELL格式存储
    blockColIndices.resize(mb * maxBlocksPerRow, -1);  // -1表示空块
    blockValues.resize(mb * maxBlocksPerRow * blockSize * blockSize, __float2half(0.0f));
    
    // 为每个块行生成稀疏模式
    for (int brow = 0; brow < mb; brow++) {
        int numActiveBlocks = 0;
        
        for (int bcol = 0; bcol < kb && numActiveBlocks < maxBlocksPerRow; bcol++) {
            // 根据稀疏度决定是否为活跃块
            if ((float)rand() / RAND_MAX > sparsity) {
                // 记录块列索引
                blockColIndices[brow * maxBlocksPerRow + numActiveBlocks] = bcol;
                
                // 生成块内的随机值
                for (int bi = 0; bi < blockSize; bi++) {
                    for (int bj = 0; bj < blockSize; bj++) {
                        int row = brow * blockSize + bi;
                        int col = bcol * blockSize + bj;
                        
                        if (row < M && col < K) {
                            half value = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);
                            matrix[row * K + col] = value;
                            
                            // 存储到BELL格式（列主序）
                            size_t base = ((size_t)brow * maxBlocksPerRow + numActiveBlocks) *
                                         (size_t)blockSize * blockSize;
                            size_t blockValueIndex = base + (size_t)bj * blockSize + bi;  // 列主序
                            blockValues[blockValueIndex] = value;
                        }
                    }
                }
                numActiveBlocks++;
            }
        }
    }
    
    printf("Generated block sparse matrix with %.1f%% sparsity\n", sparsity * 100.0f);
}

// 生成随机稠密矩阵
void generateDenseMatrix(half* matrix, int K, int N) {
    for (int i = 0; i < K * N; i++) {
        matrix[i] = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);  // [-1, 1]
    }
}

// 矩阵维度 (可调整测试不同规模)
const int M_GLOBAL = 18944;   // 稀疏矩阵A的行数
const int K_GLOBAL = 3584;   // 稀疏矩阵A的列数 / 密集矩阵B的行数
const int N_GLOBAL = 32;    // 密集矩阵B的列数

int main(int argc, char* argv[]) {
    printf("=== cuSPARSE BELL格式SPMM性能测试 ===\n");
    printf("矩阵维度: A(%d x %d) × B(%d x %d) = C(%d x %d)\n", 
           M_GLOBAL, K_GLOBAL, K_GLOBAL, N_GLOBAL, M_GLOBAL, N_GLOBAL);
    
    // 测试不同的块大小
    std::vector<int> blockSizes = {16, 32};  // Tensor Core友好的块大小
    float sparsity = 0.6f;  // 60%稀疏度
    
    for (int blockSize : blockSizes) {
        printf("\n=== 测试块大小: %d x %d ===\n", blockSize, blockSize);
        
        // ========== 步骤1: 数据准备 ==========
        printf("步骤1: 生成测试数据\n");
        
        // CPU内存分配
        half* A_h = new half[M_GLOBAL * K_GLOBAL];
        half* B_h = new half[K_GLOBAL * N_GLOBAL];
        half* C_bell_h = new half[M_GLOBAL * N_GLOBAL];
        half* C_cublas_h = new half[M_GLOBAL * N_GLOBAL];
        
        // 生成BELL格式稀疏矩阵
        std::vector<int> blockColIndices;
        std::vector<half> blockValues;
        generateBlockSparseMatrix(A_h, M_GLOBAL, K_GLOBAL, blockSize, sparsity, 
                                blockColIndices, blockValues);
        
        // 生成密集矩阵B
        generateDenseMatrix(B_h, K_GLOBAL, N_GLOBAL);
        
        // 初始化结果矩阵
        memset(C_bell_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
        memset(C_cublas_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
        
        printf("数据生成完成: A(%.1f%% 稀疏), B(密集)\n", sparsity * 100.0f);
        
        // ========== 步骤2: GPU内存分配 ==========
        printf("步骤2: GPU内存分配\n");
        
        // BELL格式GPU内存
        int mb = (M_GLOBAL + blockSize - 1) / blockSize;
        int maxBlocksPerRow = blockColIndices.size() / mb;
        
        int* d_blockColIndices = nullptr;
        half* d_blockValues = nullptr;
        half* d_B = nullptr;
        half* d_C_bell = nullptr;
        half* d_C_cublas = nullptr;
        half* d_A_dense = nullptr;  // 用于cuBLAS基准测试
        
        CHECK_CUDA(cudaMalloc(&d_blockColIndices, blockColIndices.size() * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&d_blockValues, blockValues.size() * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&d_B, K_GLOBAL * N_GLOBAL * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&d_C_bell, M_GLOBAL * N_GLOBAL * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&d_C_cublas, M_GLOBAL * N_GLOBAL * sizeof(half)));
        CHECK_CUDA(cudaMalloc(&d_A_dense, M_GLOBAL * K_GLOBAL * sizeof(half)));
        
        // 数据传输到GPU
        CHECK_CUDA(cudaMemcpy(d_blockColIndices, blockColIndices.data(), 
                             blockColIndices.size() * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(d_blockValues, blockValues.data(), 
                             blockValues.size() * sizeof(half), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(d_B, B_h, K_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(d_A_dense, A_h, M_GLOBAL * K_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
        
        printf("GPU内存分配完成\n");
        
        // ========== 步骤3: cuSPARSE BELL格式SpMM ==========
        printf("步骤3: cuSPARSE BELL格式SpMM测试\n");
        
        // 创建cuSPARSE句柄和描述符
        cusparseHandle_t handle;
        CHECK_CUSPARSE(cusparseCreate(&handle));
        
        cusparseSpMatDescr_t matA;
        cusparseDnMatDescr_t matB, matC;
        
        // 数据类型设置 (关键：使用FP16启用Tensor Core)
        cudaDataType A_B_type = CUDA_R_16F;    // A/B存储类型 (half)
        cudaDataType C_type = CUDA_R_16F;      // C存储类型 (half)
        cudaDataType compute = CUDA_R_32F;     // 计算类型 (float)
        
        // 创建Blocked-ELL稀疏矩阵描述符
        CHECK_CUSPARSE(cusparseCreateBlockedEll(
            &matA,
            M_GLOBAL, K_GLOBAL,                 // A矩阵尺寸
            blockSize,                          // 块大小 (必须是方块)
            maxBlocksPerRow,                    // 每行最大块数
            d_blockColIndices, d_blockValues,   // 块列索引和值
            CUSPARSE_INDEX_32I,                 // 索引类型
            CUSPARSE_INDEX_BASE_ZERO,           // 0基索引
            A_B_type));                         // 数据类型
        
        // 创建密集矩阵描述符 (行主序)
        int ldb = N_GLOBAL, ldc = N_GLOBAL;
        CHECK_CUSPARSE(cusparseCreateDnMat(&matB, K_GLOBAL, N_GLOBAL, ldb, d_B, A_B_type, CUSPARSE_ORDER_ROW));
        CHECK_CUSPARSE(cusparseCreateDnMat(&matC, M_GLOBAL, N_GLOBAL, ldc, d_C_bell, C_type, CUSPARSE_ORDER_ROW));
        
        // SpMM参数
        float alpha = 1.0f, beta = 0.0f;
        
        // 查询缓冲区大小
        size_t bufferSize = 0;
        CHECK_CUSPARSE(cusparseSpMM_bufferSize(
            handle,
            CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
            &alpha, matA, matB, &beta, matC, compute,
            CUSPARSE_SPMM_ALG_DEFAULT, &bufferSize));
        
        void* dBuffer = nullptr;
        CHECK_CUDA(cudaMalloc(&dBuffer, bufferSize));
        
        printf("缓冲区大小: %.2f MB\n", bufferSize / (1024.0f * 1024.0f));
        
        // 性能测试事件
        cudaEvent_t start, stop;
        CHECK_CUDA(cudaEventCreate(&start));
        CHECK_CUDA(cudaEventCreate(&stop));
        
        // BELL格式SpMM预热
        for (int i = 0; i < WARM_UP_ITERATION; i++) {
            CHECK_CUSPARSE(cusparseSpMM(
                handle,
                CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
                &alpha, matA, matB, &beta, matC, compute,
                CUSPARSE_SPMM_ALG_DEFAULT, dBuffer));
        }
        CHECK_CUDA(cudaDeviceSynchronize());
        
        // BELL格式SpMM计时
        CHECK_CUDA(cudaEventRecord(start));
        for (int i = 0; i < BENCHMARK_ITERATION; i++) {
            nvtxRangePushA("BELL SpMM iteration");
            CHECK_CUSPARSE(cusparseSpMM(
                handle,
                CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
                &alpha, matA, matB, &beta, matC, compute,
                CUSPARSE_SPMM_ALG_DEFAULT, dBuffer));
            nvtxRangePop();
        }
        CHECK_CUDA(cudaEventRecord(stop));
        CHECK_CUDA(cudaEventSynchronize(stop));
        
        float bell_time;
        CHECK_CUDA(cudaEventElapsedTime(&bell_time, start, stop));
        bell_time /= BENCHMARK_ITERATION;
        
        // 计算TFLOPS (考虑稀疏度)
        double ops = 2.0 * M_GLOBAL * N_GLOBAL * K_GLOBAL * (1.0 - sparsity);
        float tflops_bell = (ops / (bell_time / 1000.0)) / 1e12;
        
        printf("BELL SpMM完成\n");
        
        // ========== 步骤4: cuBLAS基准测试 ==========
        printf("步骤4: cuBLAS基准测试\n");
        
        cublasHandle_t cublasHandle;
        CHECK_CUBLAS(cublasCreate(&cublasHandle));
        CHECK_CUBLAS(cublasSetMathMode(cublasHandle, CUBLAS_TENSOR_OP_MATH));  // 启用Tensor Core
        
        // cuBLAS预热
        for (int i = 0; i < WARM_UP_ITERATION; i++) {
            CHECK_CUBLAS(cublasGemmEx(cublasHandle, CUBLAS_OP_N, CUBLAS_OP_N,
                         M_GLOBAL, N_GLOBAL, K_GLOBAL,
                         &alpha, d_A_dense, CUDA_R_16F, M_GLOBAL,
                         d_B, CUDA_R_16F, K_GLOBAL,
                         &beta, d_C_cublas, CUDA_R_16F, M_GLOBAL,
                         CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0)));
        }
        CHECK_CUDA(cudaDeviceSynchronize());

        // cuBLAS计时
        CHECK_CUDA(cudaEventRecord(start));
        for (int i = 0; i < BENCHMARK_ITERATION; i++) {
            CHECK_CUBLAS(cublasGemmEx(cublasHandle, CUBLAS_OP_N, CUBLAS_OP_N,
                         M_GLOBAL, N_GLOBAL, K_GLOBAL,
                         &alpha, d_A_dense, CUDA_R_16F, M_GLOBAL,
                         d_B, CUDA_R_16F, K_GLOBAL,
                         &beta, d_C_cublas, CUDA_R_16F, M_GLOBAL,
                         CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0)));
        }
        CHECK_CUDA(cudaEventRecord(stop));
        CHECK_CUDA(cudaEventSynchronize(stop));
        
        float cublas_time;
        CHECK_CUDA(cudaEventElapsedTime(&cublas_time, start, stop));
        cublas_time /= BENCHMARK_ITERATION;
        
        double ops_dense = 2.0 * M_GLOBAL * N_GLOBAL * K_GLOBAL;
        float tflops_cublas = (ops_dense / (cublas_time / 1000.0)) / 1e12;
        
        printf("cuBLAS测试完成\n");
        
        // ========== 步骤5: 结果验证和性能分析 ==========
        printf("步骤5: 结果验证和性能分析\n");
        
        // 拷贝结果回CPU
        CHECK_CUDA(cudaMemcpy(C_bell_h, d_C_bell, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));
        CHECK_CUDA(cudaMemcpy(C_cublas_h, d_C_cublas, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));
        
        // 计算误差
        double error = ComputeError(C_cublas_h, C_bell_h, M_GLOBAL, N_GLOBAL);
        
        printf("\n=== 块大小 %dx%d 性能结果 ===\n", blockSize, blockSize);
        PrintPerformance("cuBLAS (密集)", cublas_time, tflops_cublas);
        PrintPerformance("BELL (稀疏)", bell_time, tflops_bell, error);
        
        float speedup = cublas_time / bell_time;
        float efficiency = tflops_bell / tflops_cublas;
        
        printf("加速比: %.2fx\n", speedup);
        printf("效率比: %.2f%% (理论最大: %.1f%%)\n", efficiency * 100.0f, (1.0f - sparsity) * 100.0f);
        printf("数值误差: %.2e\n", error);
        
        // ========== 清理资源 ==========
        CHECK_CUSPARSE(cusparseDestroySpMat(matA));
        CHECK_CUSPARSE(cusparseDestroyDnMat(matB));
        CHECK_CUSPARSE(cusparseDestroyDnMat(matC));
        CHECK_CUSPARSE(cusparseDestroy(handle));
        CHECK_CUBLAS(cublasDestroy(cublasHandle));
        
        CHECK_CUDA(cudaFree(dBuffer));
        CHECK_CUDA(cudaFree(d_blockColIndices));
        CHECK_CUDA(cudaFree(d_blockValues));
        CHECK_CUDA(cudaFree(d_B));
        CHECK_CUDA(cudaFree(d_C_bell));
        CHECK_CUDA(cudaFree(d_C_cublas));
        CHECK_CUDA(cudaFree(d_A_dense));
        
        CHECK_CUDA(cudaEventDestroy(start));
        CHECK_CUDA(cudaEventDestroy(stop));
        
        // 清理CPU内存
        delete[] A_h;
        delete[] B_h;
        delete[] C_bell_h;
        delete[] C_cublas_h;
    }
    
    printf("\n=== BELL格式SPMM测试完成 ===\n");
    return 0;
}
