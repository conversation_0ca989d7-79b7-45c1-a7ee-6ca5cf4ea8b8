# cuSPARSE BELL格式SPMM性能测试

## 概述

本程序实现了基于cuSPARSE库的Blocked-ELL (BELL)格式稀疏矩阵乘法性能测试，专门针对NVIDIA Tensor Core进行优化。

## 技术特点

### BELL格式优势
- **块稀疏存储**：将稀疏矩阵组织成固定大小的块，减少内存碎片
- **Tensor Core加速**：利用FP16数据类型和块结构，自动启用Tensor Core计算
- **内存访问优化**：规则的块访问模式，提高内存带宽利用率
- **计算效率**：跳过零块的计算，显著提升稀疏矩阵性能

### 实现细节
- **数据类型**：使用FP16存储，FP32计算，自动启用Tensor Core
- **块大小**：支持16x16和32x32块，针对不同GPU架构优化
- **稀疏度**：可配置稀疏度，测试不同稀疏模式的性能
- **性能对比**：与cuBLAS密集GEMM进行性能和精度对比

## 编译和运行

### 编译
```bash
cd SpInfer
make -f Makefile_three_kernels test_bell_spmm
```

### 运行
```bash
make -f Makefile_three_kernels run_bell_spmm
```

## 程序流程

### 1. 数据准备
- 生成指定稀疏度的块稀疏矩阵A
- 生成随机密集矩阵B
- 转换为BELL格式存储

### 2. GPU内存管理
- 分配BELL格式GPU内存（块索引+块值）
- 分配密集矩阵GPU内存
- 数据传输到GPU

### 3. cuSPARSE BELL SpMM
- 创建Blocked-ELL稀疏矩阵描述符
- 配置Tensor Core友好的数据类型
- 执行稀疏矩阵乘法

### 4. cuBLAS基准测试
- 执行等价的密集矩阵乘法
- 启用Tensor Core进行公平对比

### 5. 性能分析
- 计算TFLOPS性能
- 分析加速比和效率
- 验证数值精度

## 性能指标

### 关键指标
- **执行时间**：毫秒级精度计时
- **TFLOPS**：考虑稀疏度的有效计算量
- **加速比**：相对于密集GEMM的性能提升
- **效率比**：实际性能与理论最大性能的比值
- **数值误差**：与密集计算结果的差异

### 预期性能
根据NVIDIA官方数据，在V100和A100 GPU上：
- **高稀疏度**（>90%）：显著优于密集计算
- **中等稀疏度**（70-90%）：与稀疏度成正比的性能提升
- **低稀疏度**（<70%）：可能不如密集计算

## 配置参数

### 矩阵维度
```cpp
const int M_GLOBAL = 2048;   // 稀疏矩阵A的行数
const int K_GLOBAL = 2048;   // 稀疏矩阵A的列数
const int N_GLOBAL = 512;    // 密集矩阵B的列数
```

### 块大小
```cpp
std::vector<int> blockSizes = {16, 32};  // 测试不同块大小
```

### 稀疏度
```cpp
float sparsity = 0.8f;  // 80%稀疏度
```

### 性能测试参数
```cpp
#define WARM_UP_ITERATION 5    // 预热次数
#define BENCHMARK_ITERATION 10 // 基准测试次数
```

## 技术要求

### 硬件要求
- **GPU架构**：Volta (V100) 或更新架构
- **计算能力**：7.0或更高
- **内存**：足够存储测试矩阵的GPU内存

### 软件要求
- **CUDA Toolkit**：11.4或更高版本
- **cuSPARSE**：支持Blocked-ELL格式的版本
- **cuBLAS**：支持Tensor Core的版本

## 优化建议

### 性能优化
1. **块大小选择**：
   - 16x16：适合较小矩阵和高稀疏度
   - 32x32：适合大矩阵和中等稀疏度

2. **内存对齐**：
   - 使用128字节对齐的指针
   - 优化内存访问模式

3. **稀疏模式**：
   - 结构化稀疏模式性能更好
   - 避免极不规则的稀疏分布

### 调试技巧
1. **NVTX标记**：使用Nsight Systems分析性能
2. **错误检查**：启用详细的CUDA和cuSPARSE错误检查
3. **内存检查**：使用cuda-memcheck检测内存错误

## 扩展方向

### 1. 多精度支持
- 添加INT8、BF16等数据类型支持
- 混合精度计算优化

### 2. 批量处理
- 支持批量稀疏矩阵乘法
- 多流并行处理

### 3. 自适应优化
- 运行时块大小选择
- 动态稀疏度阈值

### 4. 与其他格式对比
- CSR格式性能对比
- COO格式性能对比
- 结构化稀疏（2:4）对比

## 参考资料

1. [NVIDIA cuSPARSE文档](https://docs.nvidia.com/cuda/cusparse/)
2. [Accelerating Matrix Multiplication with Block Sparse Format and NVIDIA Tensor Cores](https://developer.nvidia.com/blog/accelerating-matrix-multiplication-with-block-sparse-format-and-nvidia-tensor-cores/)
3. [NVIDIA Tensor Core编程指南](https://docs.nvidia.com/cuda/tensor-core/)
4. [cuSPARSE示例代码](https://github.com/NVIDIA/CUDALibrarySamples/tree/master/cuSPARSE)

## 故障排除

### 常见问题
1. **编译错误**：检查CUDA Toolkit版本和cuSPARSE支持
2. **运行时错误**：验证GPU架构和驱动版本
3. **性能异常**：检查Tensor Core是否正确启用
4. **内存不足**：调整矩阵维度或使用更大GPU内存

### 调试命令
```bash
# 检查GPU信息
nvidia-smi

# 检查CUDA版本
nvcc --version

# 使用Nsight Systems分析
nsys profile ./test_bell_spmm
```
