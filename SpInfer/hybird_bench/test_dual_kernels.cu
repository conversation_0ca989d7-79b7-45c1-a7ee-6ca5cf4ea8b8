/***************************************************************************
 * 双内核测试程序 - 顺序运行内核1和内核2
 * 测试Matrix1(TC)和Matrix2(CC)的独立执行和性能对比
 ***************************************************************************/
#include <iostream>
#include <vector>
#include <cmath>
#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <cusparse_v2.h>
#include "../build/SpMM_API.cuh"
#include "../kernel_benchmark/Flashllm_utils.cuh"
#include <nvtx3/nvToolsExt.h>

// 性能测试常量
#define WARM_UP_ITERATION 2
#define BENCHMARK_ITERATION 5

// 错误检查宏
#define CHECK_CUDA(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        exit(1); \
    } \
} while(0)

// 性能对比工具函数
void PrintPerformance(const char* KernelName, float milliseconds, float tflops, double error = 0.0) {
    printf("%-15s -> Time: %6.3f ms, Performance: %6.2f TFLOPS",
           KernelName, milliseconds, tflops);
    if (error > 0.0) {
        printf(", Error: %.2e", error);
    }
    printf("\n");
}

double ComputeTotalError(half* reference, half* result, int M, int N) {
    double totalError = 0.0;
    for (int i = 0; i < M * N; i++) {
        double diff = __half2float(reference[i]) - __half2float(result[i]);
        totalError += diff * diff;
    }
    return sqrt(totalError / (M * N));
}

// 矩阵维度 (Qwen2.5 FFN参数)
const int M_GLOBAL = 18944;
const int K_GLOBAL = 3584;
const int N_GLOBAL = 32;

// 生成随机稀疏矩阵
void generateSparseMatrix(half* matrix, int M, int K, float sparsity) {
    for (int i = 0; i < M * K; i++) {
        if ((float)rand() / RAND_MAX < sparsity) {
            matrix[i] = __float2half(0.0f);  // 稀疏元素
        } else {
            matrix[i] = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);  // [-1, 1]
        }
    }
}

// 生成随机稠密矩阵
void generateDenseMatrix(half* matrix, int K, int N) {
    for (int i = 0; i < K * N; i++) {
        matrix[i] = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);  // [-1, 1]
    }
}

int main(int argc, char* argv[]) {
    printf("=== 双内核测试程序（顺序执行）===\n");
    printf("矩阵维度: A(%d x %d) × B(%d x %d) = C(%d x %d)\n", 
           M_GLOBAL, K_GLOBAL, K_GLOBAL, N_GLOBAL, M_GLOBAL, N_GLOBAL);
    printf("内核1: Matrix1_TC (块稀疏，60%%稀疏度)\n");
    printf("内核2: Matrix2_CC (稀疏块，90%%稀疏度)\n\n");

    // ========== 步骤1: 数据准备 ==========
    printf("步骤1: 准备双内核测试数据\n");
    
    // 内核1数据 (块稀疏，60%稀疏度)
    half* A1_h = new half[M_GLOBAL * K_GLOBAL];
    half* B1_h = new half[K_GLOBAL * N_GLOBAL];
    half* C1_h = new half[M_GLOBAL * N_GLOBAL];
    generateSparseMatrix(A1_h, M_GLOBAL, K_GLOBAL, 0.6f);
    generateDenseMatrix(B1_h, K_GLOBAL, N_GLOBAL);
    memset(C1_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    
    // 内核2数据 (稀疏块，90%稀疏度)
    half* A2_h = new half[M_GLOBAL * K_GLOBAL];
    half* B2_h = new half[K_GLOBAL * N_GLOBAL];
    half* C2_h = new half[M_GLOBAL * N_GLOBAL];
    generateSparseMatrix(A2_h, M_GLOBAL, K_GLOBAL, 0.9f);
    generateDenseMatrix(B2_h, K_GLOBAL, N_GLOBAL);
    memset(C2_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    
    printf("内核1数据生成完成: A1(%dx%d, 60%%稀疏), B1(%dx%d)\n", M_GLOBAL, K_GLOBAL, K_GLOBAL, N_GLOBAL);
    printf("内核2数据生成完成: A2(%dx%d, 90%%稀疏), B2(%dx%d)\n", M_GLOBAL, K_GLOBAL, K_GLOBAL, N_GLOBAL);

    // ========== 步骤2: 稀疏格式转换 ==========
    printf("\n步骤2: 稀疏格式转换\n");
    
    // 内核1转换 (TC格式)
    half* Compressed_Val1_cpu = nullptr;
    int* TileOffsets1_cpu = nullptr;
    int* TileOffsets1_median_cpu = nullptr;
    int* TileOffsets1_global_cpu = nullptr;
    uint64_t* bitmap1_cpu = nullptr;
    int max_nnz_intile1 = 0;
    
    int num_global_tiles1 = InitSparseMatrixA_bitmap_TC(
        A1_h, M_GLOBAL, K_GLOBAL,
        8, 16, 64,    // tile_M, tile_M_median, tile_M_global
        8, 64, 64,    // tile_K, tile_K_median, tile_K_global
        &Compressed_Val1_cpu, &TileOffsets1_cpu, &TileOffsets1_median_cpu,
        &TileOffsets1_global_cpu, &bitmap1_cpu, max_nnz_intile1
    );
    
    // 内核2转换 (CC格式)
    half* Compressed_Val2_cpu = nullptr;
    int* TileOffsets2_cpu = nullptr;
    int* TileOffsets2_median_cpu = nullptr;
    int* TileOffsets2_global_cpu = nullptr;
    uint64_t* bitmap2_cpu = nullptr;
    int max_nnz_intile2 = 0;
    
    int num_global_tiles2 = InitSparseMatrixA_bitmap_CC(
        A2_h, M_GLOBAL, K_GLOBAL,
        8, 16, 64,    // tile_M, tile_M_median, tile_M_global
        8, 64, 64,    // tile_K, tile_K_median, tile_K_global
        &Compressed_Val2_cpu, &TileOffsets2_cpu, &TileOffsets2_median_cpu,
        &TileOffsets2_global_cpu, &bitmap2_cpu, max_nnz_intile2
    );
    
    printf("内核1转换完成: %d个全局块, 最大块内非零元素: %d\n", num_global_tiles1, max_nnz_intile1);
    printf("内核2转换完成: %d个全局块, 最大块内非零元素: %d\n", num_global_tiles2, max_nnz_intile2);

    // ========== 步骤3: GPU内存分配 ==========
    printf("\n步骤3: GPU内存分配\n");
    
    // 内核1 GPU内存
    half *A1_gpu, *Compressed_Val1_gpu, *B1_gpu, *C1_gpu;
    int *TileOffsets1_global_gpu, *TileOffsets1_median_gpu, *max_nnz_intile1_gpu;
    uint64_t *bitmap1_gpu;
    half *Reduction_Workspace1;
    
    CHECK_CUDA(cudaMalloc(&A1_gpu, M_GLOBAL * K_GLOBAL * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&Compressed_Val1_gpu, TileOffsets1_global_cpu[num_global_tiles1] * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&B1_gpu, K_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&C1_gpu, M_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&TileOffsets1_global_gpu, (num_global_tiles1 + 1) * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&TileOffsets1_median_gpu, (num_global_tiles1 * 4 + 1) * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&bitmap1_gpu, num_global_tiles1 * 64 * sizeof(uint64_t)));
    CHECK_CUDA(cudaMalloc(&max_nnz_intile1_gpu, sizeof(int)));
    CHECK_CUDA(cudaMalloc(&Reduction_Workspace1, M_GLOBAL * N_GLOBAL * 4 * sizeof(half)));
    
    // 内核2 GPU内存
    half *A2_gpu, *Compressed_Val2_gpu, *B2_gpu, *C2_gpu;
    int *TileOffsets2_global_gpu, *TileOffsets2_median_gpu, *max_nnz_intile2_gpu;
    uint64_t *bitmap2_gpu;
    half *Reduction_Workspace2;
    
    CHECK_CUDA(cudaMalloc(&A2_gpu, M_GLOBAL * K_GLOBAL * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&Compressed_Val2_gpu, TileOffsets2_global_cpu[num_global_tiles2] * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&B2_gpu, K_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&C2_gpu, M_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&TileOffsets2_global_gpu, (num_global_tiles2 + 1) * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&TileOffsets2_median_gpu, (num_global_tiles2 * 4 + 1) * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&bitmap2_gpu, num_global_tiles2 * 64 * sizeof(uint64_t)));
    CHECK_CUDA(cudaMalloc(&max_nnz_intile2_gpu, sizeof(int)));
    CHECK_CUDA(cudaMalloc(&Reduction_Workspace2, M_GLOBAL * N_GLOBAL * 4 * sizeof(half)));
    
    printf("内核1 GPU内存分配完成\n");
    printf("内核2 GPU内存分配完成\n");

    // ========== 步骤4: 数据传输到GPU ==========
    printf("\n步骤4: 数据传输到GPU\n");

    // 内核1数据传输
    CHECK_CUDA(cudaMemcpy(A1_gpu, A1_h, M_GLOBAL * K_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(Compressed_Val1_gpu, Compressed_Val1_cpu, TileOffsets1_global_cpu[num_global_tiles1] * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(B1_gpu, B1_h, K_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(TileOffsets1_global_gpu, TileOffsets1_global_cpu, (num_global_tiles1 + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(TileOffsets1_median_gpu, TileOffsets1_median_cpu, (num_global_tiles1 * 4 + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(bitmap1_gpu, bitmap1_cpu, num_global_tiles1 * 64 * sizeof(uint64_t), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(max_nnz_intile1_gpu, &max_nnz_intile1, sizeof(int), cudaMemcpyHostToDevice));

    // 内核2数据传输
    CHECK_CUDA(cudaMemcpy(A2_gpu, A2_h, M_GLOBAL * K_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(Compressed_Val2_gpu, Compressed_Val2_cpu, TileOffsets2_global_cpu[num_global_tiles2] * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(B2_gpu, B2_h, K_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(TileOffsets2_global_gpu, TileOffsets2_global_cpu, (num_global_tiles2 + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(TileOffsets2_median_gpu, TileOffsets2_median_cpu, (num_global_tiles2 * 4 + 1) * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(bitmap2_gpu, bitmap2_cpu, num_global_tiles2 * 64 * sizeof(uint64_t), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(max_nnz_intile2_gpu, &max_nnz_intile2, sizeof(int), cudaMemcpyHostToDevice));

    printf("内核1数据传输完成\n");
    printf("内核2数据传输完成\n");

    // ========== 步骤5: 创建CUDA Streams ==========
    printf("\n步骤5: 创建非阻塞CUDA Streams用于真正并行执行\n");

    // 创建两个独立的非阻塞CUDA streams
    cudaStream_t stream1, stream2;
    CHECK_CUDA(cudaStreamCreateWithFlags(&stream1, cudaStreamNonBlocking));
    CHECK_CUDA(cudaStreamCreateWithFlags(&stream2, cudaStreamNonBlocking));

    // 创建CUDA事件用于计时
    cudaEvent_t start, stop;
    cudaEvent_t start1, stop1, start2, stop2;  // 用于单独计时
    CHECK_CUDA(cudaEventCreateWithFlags(&start, cudaEventDefault));
    CHECK_CUDA(cudaEventCreateWithFlags(&stop, cudaEventDefault));
    CHECK_CUDA(cudaEventCreateWithFlags(&start1, cudaEventDefault));
    CHECK_CUDA(cudaEventCreateWithFlags(&stop1, cudaEventDefault));
    CHECK_CUDA(cudaEventCreateWithFlags(&start2, cudaEventDefault));
    CHECK_CUDA(cudaEventCreateWithFlags(&stop2, cudaEventDefault));

    printf("Stream1: 内核1 (Matrix1_TC) - 非阻塞流\n");
    printf("Stream2: 内核2 (Matrix2_CC) - 非阻塞流\n");

    // 并行优化：减少Split_K以降低资源竞争
    const int Split_K = 2;  // 从4减少到2，减少并发压力

    printf("优化设置: Split_K = %d (减少资源竞争)\n", Split_K);

    // ========== 步骤6: 单独性能测试（用于对比） ==========
    printf("\n步骤6: 单独内核性能测试\n");

    // === 内核1单独测试（使用默认stream） ===
    printf("内核1 (Matrix1_TC) 单独性能测试（默认stream）...\n");
    // 预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        nvtxRangePushA("TC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_TC(0, A1_gpu, Compressed_Val1_gpu,
                                      TileOffsets1_global_gpu, TileOffsets1_median_gpu,
                                      bitmap1_gpu, max_nnz_intile1_gpu, B1_gpu, C1_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace1, Split_K));
        nvtxRangePop();
    }
    CHECK_CUDA(cudaDeviceSynchronize());

    // 计时
    CHECK_CUDA(cudaEventRecord(start1, 0));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        nvtxRangePushA("TC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_TC(0, A1_gpu, Compressed_Val1_gpu,
                                      TileOffsets1_global_gpu, TileOffsets1_median_gpu,
                                      bitmap1_gpu, max_nnz_intile1_gpu, B1_gpu, C1_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace1, Split_K));
        nvtxRangePop();
    }
    CHECK_CUDA(cudaEventRecord(stop1, 0));
    CHECK_CUDA(cudaEventSynchronize(stop1));

    float kernel1_time;
    CHECK_CUDA(cudaEventElapsedTime(&kernel1_time, start1, stop1));
    kernel1_time /= BENCHMARK_ITERATION;

    // === 内核2单独测试（使用默认stream） ===
    printf("内核2 (Matrix2_CC) 单独性能测试（默认stream）...\n");
    // 预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        nvtxRangePushA("CC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_CC(0, A2_gpu, Compressed_Val2_gpu,
                                      TileOffsets2_global_gpu, TileOffsets2_median_gpu,
                                      bitmap2_gpu, max_nnz_intile2_gpu, B2_gpu, C2_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace2, Split_K));
        nvtxRangePop();
    }
    CHECK_CUDA(cudaDeviceSynchronize());

    // 计时
    CHECK_CUDA(cudaEventRecord(start2, 0));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        nvtxRangePushA("CC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_CC(0, A2_gpu, Compressed_Val2_gpu,
                                      TileOffsets2_global_gpu, TileOffsets2_median_gpu,
                                      bitmap2_gpu, max_nnz_intile2_gpu, B2_gpu, C2_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace2, Split_K));
        nvtxRangePop();
    }
    CHECK_CUDA(cudaEventRecord(stop2, 0));
    CHECK_CUDA(cudaEventSynchronize(stop2));

    float kernel2_time;
    CHECK_CUDA(cudaEventElapsedTime(&kernel2_time, start2, stop2));
    kernel2_time /= BENCHMARK_ITERATION;

    // ========== 步骤7: 并行执行测试（使用非阻塞stream） ==========
    printf("\n步骤7: Stream并行执行测试（使用非阻塞stream）\n");
    printf("并行执行内核1和内核2...\n");

    // 并行执行预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        // 同时启动两个内核在不同的stream中
        nvtxRangePushA("TC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_TC(stream1, A1_gpu, Compressed_Val1_gpu,
                                      TileOffsets1_global_gpu, TileOffsets1_median_gpu,
                                      bitmap1_gpu, max_nnz_intile1_gpu, B1_gpu, C1_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace1, Split_K));
        nvtxRangePop();
        nvtxRangePushA("CC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_CC(stream2, A2_gpu, Compressed_Val2_gpu,
                                      TileOffsets2_global_gpu, TileOffsets2_median_gpu,
                                      bitmap2_gpu, max_nnz_intile2_gpu, B2_gpu, C2_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace2, Split_K));
        nvtxRangePop();
        // 等待两个stream都完成
        CHECK_CUDA(cudaStreamSynchronize(stream1));
        CHECK_CUDA(cudaStreamSynchronize(stream2));
    }

    // 并行执行计时 - 使用更精确的方法
    CHECK_CUDA(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        // 记录每个stream的开始时间
        CHECK_CUDA(cudaEventRecord(start1, stream1));
        CHECK_CUDA(cudaEventRecord(start2, stream2));

        // 同时启动两个内核
        nvtxRangePushA("TC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_TC(stream1, A1_gpu, Compressed_Val1_gpu,
                                      TileOffsets1_global_gpu, TileOffsets1_median_gpu,
                                      bitmap1_gpu, max_nnz_intile1_gpu, B1_gpu, C1_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace1, Split_K));
        nvtxRangePop();
        nvtxRangePushA("CC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_CC(stream2, A2_gpu, Compressed_Val2_gpu,
                                      TileOffsets2_global_gpu, TileOffsets2_median_gpu,
                                      bitmap2_gpu, max_nnz_intile2_gpu, B2_gpu, C2_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace2, Split_K));
        nvtxRangePop();

        // 记录每个stream的结束时间
        CHECK_CUDA(cudaEventRecord(stop1, stream1));
        CHECK_CUDA(cudaEventRecord(stop2, stream2));

        // 等待两个stream都完成
        CHECK_CUDA(cudaStreamSynchronize(stream1));
        CHECK_CUDA(cudaStreamSynchronize(stream2));
    }
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaEventSynchronize(stop));

    // 验证并行执行的重叠情况
    if (BENCHMARK_ITERATION == 1) {  // 只在单次执行时检查
        float stream1_time, stream2_time;
        CHECK_CUDA(cudaEventElapsedTime(&stream1_time, start1, stop1));
        CHECK_CUDA(cudaEventElapsedTime(&stream2_time, start2, stop2));
        printf("调试信息 - Stream1执行时间: %.3f ms\n", stream1_time);
        printf("调试信息 - Stream2执行时间: %.3f ms\n", stream2_time);
        printf("调试信息 - 理论最小并行时间: %.3f ms\n", fmax(stream1_time, stream2_time));
    }

    float total_parallel_time;
    CHECK_CUDA(cudaEventElapsedTime(&total_parallel_time, start, stop));
    total_parallel_time /= BENCHMARK_ITERATION;

    // ========== 步骤8: 顺序执行对比测试 ==========
    printf("\n步骤8: 顺序执行对比测试\n");
    printf("顺序执行内核1和内核2...\n");

    CHECK_CUDA(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        // 顺序执行两个内核（都在默认stream）
        nvtxRangePushA("TC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_TC(0, A1_gpu, Compressed_Val1_gpu,
                                      TileOffsets1_global_gpu, TileOffsets1_median_gpu,
                                      bitmap1_gpu, max_nnz_intile1_gpu, B1_gpu, C1_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace1, Split_K));
        nvtxRangePop();
        nvtxRangePushA("CC SpMM iteration");
        CHECK_CUDA(SpMM_SplitK_API_bitmap_v3_CC(0, A2_gpu, Compressed_Val2_gpu,
                                      TileOffsets2_global_gpu, TileOffsets2_median_gpu,
                                      bitmap2_gpu, max_nnz_intile2_gpu, B2_gpu, C2_gpu,
                                      M_GLOBAL, N_GLOBAL, K_GLOBAL, Reduction_Workspace2, Split_K));
        nvtxRangePop();
    }
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaEventSynchronize(stop));

    float total_sequential_time;
    CHECK_CUDA(cudaEventElapsedTime(&total_sequential_time, start, stop));
    total_sequential_time /= BENCHMARK_ITERATION;

    // ========== 步骤9: cuBLAS基准测试 ==========
    printf("\n步骤9: cuBLAS基准性能测试\n");

    // 计算性能指标的基础数据
    long long ops = 2LL * M_GLOBAL * N_GLOBAL * K_GLOBAL;  // 乘加操作数

    // cuBLAS测试1 (对应内核1的数据)
    printf("测试 cuBLAS 基准1 (对应内核1数据)...\n");
    half* C_cublas1_gpu = NULL;
    CHECK_CUDA(cudaMalloc(&C_cublas1_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL));
    CHECK_CUDA(cudaMemset(C_cublas1_gpu, 0, sizeof(half) * M_GLOBAL * N_GLOBAL));

    cublasHandle_t handle1;
    cublasCreate(&handle1);
    cublasSetStream(handle1, 0);  // 使用默认stream
    cublasSetMathMode(handle1, CUBLAS_DEFAULT_MATH);  // 启用Tensor Core

    const float alpha = 1.0f, beta = 0.0f;

    // cuBLAS1预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        cublasGemmEx(handle1, CUBLAS_OP_T, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, A1_gpu, CUDA_R_16F, K_GLOBAL,
                     B1_gpu, CUDA_R_16F, K_GLOBAL,
                     &beta, C_cublas1_gpu, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0));
    }
    CHECK_CUDA(cudaDeviceSynchronize());

    // cuBLAS1计时
    CHECK_CUDA(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        cublasGemmEx(handle1, CUBLAS_OP_T, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, A1_gpu, CUDA_R_16F, K_GLOBAL,
                     B1_gpu, CUDA_R_16F, K_GLOBAL,
                     &beta, C_cublas1_gpu, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0));
    }
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaEventSynchronize(stop));

    float cublas1_time;
    CHECK_CUDA(cudaEventElapsedTime(&cublas1_time, start, stop));
    cublas1_time /= BENCHMARK_ITERATION;
    float tflops_cublas1 = (ops / (cublas1_time / 1000.0)) / 1e12;

    // cuBLAS测试2 (对应内核2的数据)
    printf("测试 cuBLAS 基准2 (对应内核2数据)...\n");
    half* C_cublas2_gpu = NULL;
    CHECK_CUDA(cudaMalloc(&C_cublas2_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL));
    CHECK_CUDA(cudaMemset(C_cublas2_gpu, 0, sizeof(half) * M_GLOBAL * N_GLOBAL));

    cublasHandle_t handle2;
    cublasCreate(&handle2);
    cublasSetStream(handle2, 0);  // 使用默认stream
    cublasSetMathMode(handle2, CUBLAS_DEFAULT_MATH);  // 启用Tensor Core

    // cuBLAS2预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        cublasGemmEx(handle2, CUBLAS_OP_T, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, A2_gpu, CUDA_R_16F, K_GLOBAL,
                     B2_gpu, CUDA_R_16F, K_GLOBAL,
                     &beta, C_cublas2_gpu, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0));
    }
    CHECK_CUDA(cudaDeviceSynchronize());

    // cuBLAS2计时
    CHECK_CUDA(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        cublasGemmEx(handle2, CUBLAS_OP_T, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, A2_gpu, CUDA_R_16F, K_GLOBAL,
                     B2_gpu, CUDA_R_16F, K_GLOBAL,
                     &beta, C_cublas2_gpu, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0));
    }
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaEventSynchronize(stop));

    float cublas2_time;
    CHECK_CUDA(cudaEventElapsedTime(&cublas2_time, start, stop));
    cublas2_time /= BENCHMARK_ITERATION;
    float tflops_cublas2 = (ops / (cublas2_time / 1000.0)) / 1e12;

    // ========== 步骤10: 性能结果分析 ==========
    printf("\n=== 双内核Stream并行性能对比结果 ===\n");

    // 计算TFLOPS
    float tflops_kernel1 = (ops / (kernel1_time / 1000.0)) / 1e12;
    float tflops_kernel2 = (ops / (kernel2_time / 1000.0)) / 1e12;
    float tflops_sequential = (2 * ops / (total_sequential_time / 1000.0)) / 1e12;  // 两个矩阵乘法
    float tflops_parallel = (2 * ops / (total_parallel_time / 1000.0)) / 1e12;     // 并行执行

    printf("=== 单独性能对比 ===\n");
    printf("内核1 (Matrix1_TC):\n");
    printf("  cuBLAS 基准      -> 时间: %6.3f ms, 性能: %6.2f TFLOPS\n", cublas1_time, tflops_cublas1);
    printf("  SpInfer Matrix1  -> 时间: %6.3f ms, 性能: %6.2f TFLOPS, 加速比: %.2fx\n",
           kernel1_time, tflops_kernel1, cublas1_time / kernel1_time);
    printf("\n");
    printf("内核2 (Matrix2_CC):\n");
    printf("  cuBLAS 基准      -> 时间: %6.3f ms, 性能: %6.2f TFLOPS\n", cublas2_time, tflops_cublas2);
    printf("  SpInfer Matrix2  -> 时间: %6.3f ms, 性能: %6.2f TFLOPS, 加速比: %.2fx\n",
           kernel2_time, tflops_kernel2, cublas2_time / kernel2_time);

    printf("\n=== 并行性能分析 ===\n");
    printf("理论最佳时间 (max(内核1,内核2)): %.3f ms\n", fmax(kernel1_time, kernel2_time));
    printf("SpInfer 实际并行执行: %.3f ms, 性能: %.2f TFLOPS\n", total_parallel_time, tflops_parallel);
    printf("SpInfer 顺序执行: %.3f ms, 性能: %.2f TFLOPS\n", total_sequential_time, tflops_sequential);

    // 并行效率分析
    float parallel_efficiency = (fmax(kernel1_time, kernel2_time) / total_parallel_time) * 100;
    float parallel_speedup = total_sequential_time / total_parallel_time;
    printf("并行效率: %.1f%% (实际/理想)\n", parallel_efficiency);
    printf("并行加速比: %.2fx (顺序/并行)\n", parallel_speedup);

    printf("\n=== 整体性能对比 ===\n");
    printf("cuBLAS 基准性能: %.3f ms (单个矩阵乘法)\n", (cublas1_time + cublas2_time) / 2);
    printf("SpInfer 顺序执行: %.3f ms (两个矩阵乘法)\n", total_sequential_time);
    printf("SpInfer 并行执行: %.3f ms (两个矩阵乘法)\n", total_parallel_time);

    // 修正：与单个cuBLAS基准比较
    float avg_cublas_time = (cublas1_time + cublas2_time) / 2;
    float overall_speedup_sequential = avg_cublas_time / (total_sequential_time / 2);  // 平均每个矩阵的时间
    float overall_speedup_parallel = avg_cublas_time / (total_parallel_time / 2);     // 平均每个矩阵的时间
    printf("平均单矩阵加速比 (SpInfer顺序 vs cuBLAS): %.2fx\n", overall_speedup_sequential);
    printf("平均单矩阵加速比 (SpInfer并行 vs cuBLAS): %.2fx\n", overall_speedup_parallel);

    // ========== 步骤11: 结果验证 ==========
    printf("\n步骤11: 结果验证\n");

    // 将结果拷贝回host进行验证
    half* C1_spinfer_h = (half*)malloc(sizeof(half) * M_GLOBAL * N_GLOBAL);
    half* C1_cublas_h = (half*)malloc(sizeof(half) * M_GLOBAL * N_GLOBAL);
    half* C2_spinfer_h = (half*)malloc(sizeof(half) * M_GLOBAL * N_GLOBAL);
    half* C2_cublas_h = (half*)malloc(sizeof(half) * M_GLOBAL * N_GLOBAL);

    CHECK_CUDA(cudaMemcpy(C1_spinfer_h, C1_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL, cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaMemcpy(C1_cublas_h, C_cublas1_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL, cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaMemcpy(C2_spinfer_h, C2_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL, cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaMemcpy(C2_cublas_h, C_cublas2_gpu, sizeof(half) * M_GLOBAL * N_GLOBAL, cudaMemcpyDeviceToHost));

    // 计算误差
    double error1 = ComputeTotalError(C1_cublas_h, C1_spinfer_h, M_GLOBAL, N_GLOBAL);
    double error2 = ComputeTotalError(C2_cublas_h, C2_spinfer_h, M_GLOBAL, N_GLOBAL);

    printf("=== 结果验证 ===\n");
    printf("内核1 (Matrix1_TC):\n");
    printf("  相对误差: ");
    if (std::isnan(error1) || std::isinf(error1)) {
        printf("计算异常 (可能存在数值问题)\n");
    } else {
        printf("%.2e\n", error1);
    }

    printf("内核2 (Matrix2_CC):\n");
    printf("  相对误差: ");
    if (std::isnan(error2) || std::isinf(error2)) {
        printf("计算异常 (可能存在数值问题)\n");
    } else {
        printf("%.2e\n", error2);
    }

    // 简单验证：检查结果是否包含非零值
    int non_zero_count1 = 0, non_zero_count2 = 0;
    for (int i = 0; i < M_GLOBAL * N_GLOBAL; i++) {
        if (__half2float(C1_spinfer_h[i]) != 0.0f) non_zero_count1++;
        if (__half2float(C2_spinfer_h[i]) != 0.0f) non_zero_count2++;
    }

    printf("\n=== 输出矩阵统计 ===\n");
    printf("内核1结果矩阵: %d / %d 非零元素 (%.1f%%)\n",
           non_zero_count1, M_GLOBAL * N_GLOBAL,
           (float)non_zero_count1 / (M_GLOBAL * N_GLOBAL) * 100);
    printf("内核2结果矩阵: %d / %d 非零元素 (%.1f%%)\n",
           non_zero_count2, M_GLOBAL * N_GLOBAL,
           (float)non_zero_count2 / (M_GLOBAL * N_GLOBAL) * 100);

    // 警告检查
    if (non_zero_count2 == 0) {
        printf("⚠️  警告: 内核2输出全为零，可能存在计算问题\n");
    }

    printf("\n双内核Stream并行执行测试完成！\n");
    printf("阶段2完成：Stream并行化实现成功！\n");

    // 清理cuBLAS资源
    cublasDestroy(handle1);
    cublasDestroy(handle2);
    cudaFree(C_cublas1_gpu);
    cudaFree(C_cublas2_gpu);

    // 清理验证内存
    free(C1_spinfer_h);
    free(C1_cublas_h);
    free(C2_spinfer_h);
    free(C2_cublas_h);

    // 清理CUDA资源
    CHECK_CUDA(cudaStreamDestroy(stream1));
    CHECK_CUDA(cudaStreamDestroy(stream2));
    CHECK_CUDA(cudaEventDestroy(start));
    CHECK_CUDA(cudaEventDestroy(stop));
    CHECK_CUDA(cudaEventDestroy(start1));
    CHECK_CUDA(cudaEventDestroy(stop1));
    CHECK_CUDA(cudaEventDestroy(start2));
    CHECK_CUDA(cudaEventDestroy(stop2));

    // 清理CPU内存
    delete[] A1_h; delete[] B1_h; delete[] C1_h;
    delete[] A2_h; delete[] B2_h; delete[] C2_h;
    
    // 清理GPU内存
    cudaFree(A1_gpu); cudaFree(Compressed_Val1_gpu); cudaFree(B1_gpu); cudaFree(C1_gpu);
    cudaFree(TileOffsets1_global_gpu); cudaFree(TileOffsets1_median_gpu); 
    cudaFree(bitmap1_gpu); cudaFree(max_nnz_intile1_gpu); cudaFree(Reduction_Workspace1);
    
    cudaFree(A2_gpu); cudaFree(Compressed_Val2_gpu); cudaFree(B2_gpu); cudaFree(C2_gpu);
    cudaFree(TileOffsets2_global_gpu); cudaFree(TileOffsets2_median_gpu);
    cudaFree(bitmap2_gpu); cudaFree(max_nnz_intile2_gpu); cudaFree(Reduction_Workspace2);

    return 0;
}
