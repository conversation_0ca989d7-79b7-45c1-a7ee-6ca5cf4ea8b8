# Sputnik测试程序使用指南

## 概述

`test_sputnik.cu` 是基于Google Sputnik库实现的高稀疏度矩阵乘法测试程序，作为Matrix2 CC kernel的替代方案。专门针对90%稀疏度的稀疏矩阵进行优化。

## 功能特性

- **高稀疏度优化**: 专门针对90%稀疏度场景优化
- **性能对比**: 与cuBLAS进行详细的性能对比
- **灵活数据源**: 支持文件加载和随机生成两种数据源
- **完整验证**: 包含结果正确性验证和误差分析
- **标准化接口**: 与其他测试程序保持一致的接口和输出格式

## 编译方法

### 1. 确保依赖库已安装

```bash
# 确保sputnik库已编译
cd SpInfer/third_party/sputnik
mkdir -p build && cd build
cmake .. -DCUDA_ARCHS="86"
make -j

# 返回SpInfer根目录
cd ../../..
```

### 2. 编译测试程序

```bash
# 使用Makefile编译
make -f Makefile_three_kernels test_sputnik

# 或者编译所有程序
make -f Makefile_three_kernels all
```

## 运行方法

### 1. 基本运行

```bash
# 使用默认参数运行
make -f Makefile_three_kernels run_sputnik

# 或者直接运行
./test_sputnik
```

### 2. 自定义参数运行

```bash
# 语法: ./test_sputnik <M> <K> <N> [file] [sparsity]
./test_sputnik 18944 3584 32          # 使用随机数据，90%稀疏度
./test_sputnik 18944 3584 32 file     # 从文件加载数据
./test_sputnik 18944 3584 32 random 0.95  # 使用95%稀疏度
```

### 3. 参数说明

- **M**: 矩阵A的行数（默认: 18944）
- **K**: 矩阵A的列数/矩阵B的行数（默认: 3584）
- **N**: 矩阵B的列数（默认: 32）
- **file**: 可选，指定从文件加载矩阵数据
- **sparsity**: 可选，指定稀疏度（0.0-1.0，默认: 0.9）

## 输出示例

```
=== Sputnik测试程序（高稀疏度优化）===
矩阵维度: A(18944 x 3584) × B(3584 x 32) = C(18944 x 32)
数据源: 随机生成（90%稀疏度）

步骤1: 准备输入数据
生成自定义稀疏矩阵: M=18944, K=3584, 稀疏度=0.90
生成稠密矩阵B: K=3584, N=32

步骤2: Sputnik格式转换
Sputnik格式转换完成: 6782976个非零元素

步骤3: 数据传输到GPU
数据传输完成

步骤4: 多库性能对比测试

=== 性能对比结果 ===
测试 cuBLAS (Tensor Core)...
测试 Sputnik (高稀疏度优化)...

步骤5: 结果验证和性能对比

=== Sputnik性能对比总结 ===
cuBLAS (TC)     -> Time:  2.345 ms, Performance:  58.42 TFLOPS
Sputnik         -> Time:  1.234 ms, Performance: 111.23 TFLOPS, Error: 1.23e-04

Sputnik vs cuBLAS 加速比: 1.90x
Sputnik结果矩阵中非零元素数量: 606208 / 606208

清理资源
Sputnik测试程序执行完成!
```

## 性能特点

### 1. 适用场景

- **高稀疏度**: 80%-95%稀疏度的矩阵
- **中等规模**: M和K在1K-50K范围内
- **小批量**: N在8-256范围内

### 2. 性能优势

- **内存效率**: CSR格式减少内存占用
- **计算优化**: 跳过零元素计算
- **负载均衡**: 自动处理不规则稀疏模式

### 3. 与其他方案对比

| 方案 | 适用稀疏度 | 内存占用 | 计算效率 | 实现复杂度 |
|------|------------|----------|----------|------------|
| cuBLAS | 0%-20% | 高 | 高 | 低 |
| SpInfer Matrix2 | 70%-95% | 中 | 高 | 高 |
| Sputnik | 80%-98% | 低 | 中-高 | 中 |

## 故障排除

### 1. 编译错误

```bash
# 检查sputnik库是否正确编译
ls -la third_party/sputnik/build/sputnik/libsputnik_cuda.*

# 检查CUDA版本兼容性
nvcc --version

# 清理重新编译
make -f Makefile_three_kernels clean
make -f Makefile_three_kernels test_sputnik
```

### 2. 运行时错误

```bash
# 检查GPU架构支持
nvidia-smi

# 检查内存是否足够
nvidia-smi -q -d MEMORY

# 使用较小的矩阵测试
./test_sputnik 1024 1024 32
```

### 3. 性能问题

- **稀疏度过低**: 尝试增加稀疏度到90%以上
- **矩阵过小**: 使用更大的矩阵规模
- **内存带宽**: 检查GPU内存带宽利用率

## 集成到双内核测试

test_sputnik.cu可以作为Matrix2的替代方案集成到双内核并行测试中：

```cpp
// 在test_dual_kernels.cu中替换Matrix2调用
// 原来的Matrix2 CC调用
SpMM_SplitK_API_bitmap_v3_CC(stream2, ...);

// 替换为sputnik调用
sputnik::CudaSpmm(M_GLOBAL, K_GLOBAL, N_GLOBAL, ...);
```

## 注意事项

1. **数据格式**: sputnik要求float输入，程序会自动进行half↔float转换
2. **矩阵布局**: sputnik要求row-major的B矩阵，程序会自动转置
3. **内存对齐**: 确保输入数据正确对齐以获得最佳性能
4. **稀疏模式**: sputnik对不规则稀疏模式有很好的适应性

## 联系信息

如有问题，请参考：
- SpInfer主文档：`SpInfer_Custom_Data_Guide.md`
- Sputnik官方文档：`third_party/sputnik/README.md`
- 双内核测试：`hybird_bench/README.md`
