# cuSPARSE BELL格式SPMM实现步骤

## 概述

基于NVIDIA官方文档和博客，实现cuSPARSE Blocked-ELL格式的稀疏矩阵乘法，利用Tensor Core加速计算。

## 实现步骤

### 步骤1: 环境准备

#### 1.1 检查硬件要求
```bash
# 检查GPU架构 (需要Volta V100或更新)
nvidia-smi
nvcc --version
```

#### 1.2 验证cuSPARSE版本
```cpp
// 检查cuSPARSE版本 (需要11.4.0+)
int version;
cusparseGetVersion(handle, &version);
printf("cuSPARSE version: %d\n", version);
```

### 步骤2: 数据结构设计

#### 2.1 BELL格式存储结构
```cpp
// BELL格式参数
int blockSize = 16;           // 块大小 (16x16 或 32x32)
int mb = (M + blockSize - 1) / blockSize;  // 块行数
int kb = (K + blockSize - 1) / blockSize;  // 块列数
int maxBlocksPerRow = (int)((1.0f - sparsity) * kb) + 1;

// BELL格式存储
std::vector<int> blockColIndices(mb * maxBlocksPerRow, -1);
std::vector<half> blockValues(M * maxBlocksPerRow * blockSize);
```

#### 2.2 GPU内存分配
```cpp
// GPU内存指针
int* d_blockColIndices = nullptr;
half* d_blockValues = nullptr;
half* d_B = nullptr;           // 密集矩阵B
half* d_C = nullptr;           // 结果矩阵C

// 分配GPU内存
cudaMalloc(&d_blockColIndices, blockColIndices.size() * sizeof(int));
cudaMalloc(&d_blockValues, blockValues.size() * sizeof(half));
cudaMalloc(&d_B, K * N * sizeof(half));
cudaMalloc(&d_C, M * N * sizeof(half));
```

### 步骤3: 稀疏矩阵生成

#### 3.1 生成块稀疏模式
```cpp
void generateBlockSparseMatrix(half* matrix, int M, int K, int blockSize, 
                              float sparsity, std::vector<int>& blockColIndices, 
                              std::vector<half>& blockValues) {
    int mb = (M + blockSize - 1) / blockSize;
    int kb = (K + blockSize - 1) / blockSize;
    int maxBlocksPerRow = (int)((1.0f - sparsity) * kb) + 1;
    
    // 初始化BELL格式存储
    blockColIndices.resize(mb * maxBlocksPerRow, -1);
    blockValues.resize(M * maxBlocksPerRow * blockSize, __float2half(0.0f));
    
    // 为每个块行生成稀疏模式
    for (int brow = 0; brow < mb; brow++) {
        int numActiveBlocks = 0;
        for (int bcol = 0; bcol < kb && numActiveBlocks < maxBlocksPerRow; bcol++) {
            if ((float)rand() / RAND_MAX > sparsity) {
                // 记录块列索引
                blockColIndices[brow * maxBlocksPerRow + numActiveBlocks] = bcol;
                
                // 生成块内随机值
                for (int bi = 0; bi < blockSize; bi++) {
                    for (int bj = 0; bj < blockSize; bj++) {
                        int row = brow * blockSize + bi;
                        int col = bcol * blockSize + bj;
                        if (row < M && col < K) {
                            half value = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);
                            matrix[row * K + col] = value;
                            
                            // 存储到BELL格式
                            int blockValueIndex = (brow * maxBlocksPerRow + numActiveBlocks) * 
                                                 blockSize * blockSize + bi * blockSize + bj;
                            blockValues[blockValueIndex] = value;
                        }
                    }
                }
                numActiveBlocks++;
            }
        }
    }
}
```

### 步骤4: cuSPARSE BELL SpMM实现

#### 4.1 创建cuSPARSE句柄和描述符
```cpp
// 创建cuSPARSE句柄
cusparseHandle_t handle;
cusparseCreate(&handle);

// 创建矩阵描述符
cusparseSpMatDescr_t matA;
cusparseDnMatDescr_t matB, matC;

// 数据类型设置 (关键：FP16启用Tensor Core)
cudaDataType A_B_type = CUDA_R_16F;    // A/B存储类型
cudaDataType C_type = CUDA_R_16F;      // C存储类型  
cudaDataType compute = CUDA_R_32F;     // 计算类型
```

#### 4.2 创建Blocked-ELL稀疏矩阵描述符
```cpp
// 创建BELL格式稀疏矩阵
cusparseCreateBlockedEll(
    &matA,
    M, K,                               // 矩阵尺寸
    blockSize,                          // 块大小
    maxBlocksPerRow,                    // 每行最大块数
    d_blockColIndices, d_blockValues,   // 块索引和值
    CUSPARSE_INDEX_32I,                 // 索引类型
    CUSPARSE_INDEX_BASE_ZERO,           // 0基索引
    A_B_type);                          // 数据类型
```

#### 4.3 创建密集矩阵描述符
```cpp
// 创建密集矩阵B和C (行主序)
int ldb = N, ldc = N;
cusparseCreateDnMat(&matB, K, N, ldb, d_B, A_B_type, CUSPARSE_ORDER_ROW);
cusparseCreateDnMat(&matC, M, N, ldc, d_C, C_type, CUSPARSE_ORDER_ROW);
```

#### 4.4 执行SpMM计算
```cpp
// SpMM参数
float alpha = 1.0f, beta = 0.0f;

// 查询缓冲区大小
size_t bufferSize = 0;
cusparseSpMM_bufferSize(
    handle,
    CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
    &alpha, matA, matB, &beta, matC, compute,
    CUSPARSE_SPMM_ALG_DEFAULT, &bufferSize);

// 分配工作缓冲区
void* dBuffer = nullptr;
cudaMalloc(&dBuffer, bufferSize);

// 执行SpMM计算
cusparseSpMM(
    handle,
    CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
    &alpha, matA, matB, &beta, matC, compute,
    CUSPARSE_SPMM_ALG_DEFAULT, dBuffer);
```

### 步骤5: 性能测试框架

#### 5.1 计时和性能分析
```cpp
// 创建CUDA事件
cudaEvent_t start, stop;
cudaEventCreate(&start);
cudaEventCreate(&stop);

// 预热
for (int i = 0; i < WARM_UP_ITERATION; i++) {
    cusparseSpMM(handle, CUSPARSE_OPERATION_NON_TRANSPOSE, 
                CUSPARSE_OPERATION_NON_TRANSPOSE,
                &alpha, matA, matB, &beta, matC, compute,
                CUSPARSE_SPMM_ALG_DEFAULT, dBuffer);
}
cudaDeviceSynchronize();

// 性能测试
cudaEventRecord(start);
for (int i = 0; i < BENCHMARK_ITERATION; i++) {
    cusparseSpMM(handle, CUSPARSE_OPERATION_NON_TRANSPOSE,
                CUSPARSE_OPERATION_NON_TRANSPOSE, 
                &alpha, matA, matB, &beta, matC, compute,
                CUSPARSE_SPMM_ALG_DEFAULT, dBuffer);
}
cudaEventRecord(stop);
cudaEventSynchronize(stop);

float milliseconds;
cudaEventElapsedTime(&milliseconds, start, stop);
milliseconds /= BENCHMARK_ITERATION;

// 计算TFLOPS
double ops = 2.0 * M * N * K * (1.0 - sparsity);
float tflops = (ops / (milliseconds / 1000.0)) / 1e12;
```

#### 5.2 cuBLAS基准对比
```cpp
// 创建cuBLAS句柄
cublasHandle_t cublasHandle;
cublasCreate(&cublasHandle);
cublasSetMathMode(cublasHandle, CUBLAS_DEFAULT_MATH);  // 启用Tensor Core

// cuBLAS密集GEMM
cublasGemmEx(cublasHandle, CUBLAS_OP_T, CUBLAS_OP_N,
            M, N, K,
            &alpha, d_A_dense, CUDA_R_16F, K,
            d_B, CUDA_R_16F, K,
            &beta, d_C_cublas, CUDA_R_16F, M,
            CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0));
```

### 步骤6: 结果验证

#### 6.1 数值精度检查
```cpp
double ComputeError(half* reference, half* result, int M, int N) {
    double totalError = 0.0;
    for (int i = 0; i < M * N; i++) {
        double diff = __half2float(reference[i]) - __half2float(result[i]);
        totalError += diff * diff;
    }
    return sqrt(totalError / (M * N));
}
```

#### 6.2 性能分析
```cpp
void PrintPerformance(const char* kernelName, float milliseconds, 
                     float tflops, double error = 0.0) {
    printf("%-20s -> Time: %6.3f ms, Performance: %6.2f TFLOPS",
           kernelName, milliseconds, tflops);
    if (error > 0.0) {
        printf(", Error: %.2e", error);
    }
    printf("\n");
}

// 计算加速比和效率
float speedup = cublas_time / bell_time;
float efficiency = tflops_bell / tflops_cublas;
printf("加速比: %.2fx\n", speedup);
printf("效率比: %.2f%% (理论最大: %.1f%%)\n", 
       efficiency * 100.0f, (1.0f - sparsity) * 100.0f);
```

### 步骤7: 优化建议

#### 7.1 块大小选择
- **16x16块**: 适合高稀疏度(>90%)和较小矩阵
- **32x32块**: 适合中等稀疏度(70-90%)和大矩阵

#### 7.2 内存优化
- 使用128字节对齐的指针
- 启用Hardware Memory Compression (Ampere+)
- 考虑使用CUDA Graphs减少启动开销

#### 7.3 稀疏模式优化
- 结构化稀疏模式性能更好
- 避免极不规则的稀疏分布
- 考虑块内稀疏度的影响

## 编译和运行

### 编译命令
```bash
cd SpInfer
make -f Makefile_three_kernels test_bell_spmm
```

### 运行测试
```bash
make -f Makefile_three_kernels run_bell_spmm
```

### 性能分析
```bash
# 使用Nsight Systems分析
nsys profile ./test_bell_spmm

# 使用Nsight Compute分析
ncu --set full ./test_bell_spmm
```

## 预期结果

在V100和A100 GPU上，BELL格式SpMM相比密集GEMM的预期性能：
- **高稀疏度(90%+)**: 5-10x加速
- **中等稀疏度(80%)**: 2-4x加速  
- **低稀疏度(<70%)**: 可能不如密集计算

## 故障排除

### 常见问题
1. **编译错误**: 检查cuSPARSE版本(需要11.4.0+)
2. **运行时错误**: 验证GPU架构(需要Volta+)
3. **性能异常**: 确认Tensor Core启用(FP16数据类型)
4. **内存不足**: 调整矩阵维度或块大小

### 调试技巧
- 使用NVTX标记进行性能分析
- 启用详细的错误检查
- 验证BELL格式数据的正确性
- 对比不同块大小的性能
