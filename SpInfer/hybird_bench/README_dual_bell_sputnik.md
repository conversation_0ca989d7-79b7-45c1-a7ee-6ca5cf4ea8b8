# BELL+Sputnik混合并行测试程序

## 概述

本程序实现了cuSPARSE BELL格式和Sputnik的混合并行执行，通过CUDA Streams实现双内核非阻塞并行计算。

## 架构设计

### 双内核架构
- **内核1**: cuSPARSE BELL (块稀疏，60%稀疏度，Tensor Core优化)
- **内核2**: Sputnik (高稀疏度，90%稀疏度，CUDA Core优化)
- **并行方式**: CUDA Streams非阻塞并行执行

### 技术特点
1. **混合加速器**: Tensor Core + CUDA Core
2. **不同稀疏度**: 60% (BELL) + 90% (Sputnik)
3. **统一精度**: 都使用FP16，减少数据转换开销
4. **双Stream并行**: 最大化GPU利用率

## 实现细节

### 内核1: cuSPARSE BELL
- **格式**: Blocked-ELL稀疏矩阵格式
- **块大小**: 16x16 (适合中等稀疏度)
- **稀疏度**: 60%
- **加速器**: Tensor Core (FP16存储+FP32计算)
- **优势**: 规则的块访问模式，高内存带宽利用率

### 内核2: Sputnik
- **格式**: CSR稀疏矩阵格式
- **稀疏度**: 90%
- **加速器**: CUDA Core
- **数据类型**: half2优化
- **优势**: 对高稀疏度矩阵优化良好

### 并行执行策略
```cpp
// Stream1: BELL内核
cusparseSetStream(cusparse_handle1, stream1);
cusparseSpMM(cusparse_handle1, ...);

// Stream2: Sputnik内核 (同时执行)
sputnik::CudaSpmm(..., stream2);
```

## 性能测试框架

### 测试项目
1. **单独内核测试**: 分别测试两个内核的性能
2. **并行执行测试**: 双Stream并行执行
3. **顺序执行测试**: 用于对比并行效率
4. **cuBLAS基准测试**: 密集GEMM基准对比
5. **结果验证**: 数值精度检查

### 性能指标
- **执行时间**: 毫秒级精度计时
- **TFLOPS**: 考虑稀疏度的有效计算量
- **并行效率**: 实际并行时间 vs 理论最佳时间
- **加速比**: 相对于cuBLAS和顺序执行的性能提升
- **数值误差**: 与cuBLAS结果的差异

## 编译和运行

### 编译
```bash
cd SpInfer
make -f Makefile_three_kernels test_dual_bell_sputnik
```

### 运行
```bash
make -f Makefile_three_kernels run_dual_bell_sputnik
```

### 依赖库
- cuSPARSE (BELL格式支持)
- Sputnik (第三方稀疏库)
- cuBLAS (基准对比)
- NVTX (性能分析)

## 预期性能

### 理论分析
- **内核1 (BELL)**: 60%稀疏度，Tensor Core加速，适合中等稀疏度
- **内核2 (Sputnik)**: 90%稀疏度，CUDA Core优化，适合高稀疏度
- **并行效率**: 取决于两个内核的负载平衡

### 性能目标
- **并行效率**: >80% (接近理想并行)
- **总体加速比**: 相对于cuBLAS密集计算的显著提升
- **数值精度**: 相对误差 <1e-3

## 输出示例

```
=== BELL+Sputnik混合并行测试程序 ===
矩阵维度: A(18944 x 3584) × B(3584 x 32) = C(18944 x 32)
内核1: cuSPARSE BELL (块稀疏，60%稀疏度，Tensor Core优化)
内核2: Sputnik (高稀疏度，90%稀疏度，CUDA Core优化)

=== 单独性能对比 ===
内核1 (cuSPARSE BELL):
  cuBLAS 基准      -> 时间:  12.345 ms, 性能:  45.67 TFLOPS
  BELL SpMM        -> 时间:   8.901 ms, 性能:  63.21 TFLOPS, 加速比: 1.39x

内核2 (Sputnik):
  cuBLAS 基准      -> 时间:  12.345 ms, 性能:  45.67 TFLOPS
  Sputnik SpMM     -> 时间:   2.345 ms, 性能:  24.01 TFLOPS, 加速比: 5.26x

=== 混合并行性能分析 ===
理论最佳时间 (max(BELL,Sputnik)): 8.901 ms
BELL+Sputnik 实际并行执行: 9.123 ms, 性能: 78.45 TFLOPS
BELL+Sputnik 顺序执行: 11.246 ms, 性能: 63.67 TFLOPS

并行效率: 97.6% (1.0为理想并行)
并行加速比: 1.23x
混合并行 vs cuBLAS总体加速比: 2.71x
```

## 优化建议

### 性能优化
1. **块大小调优**: 根据稀疏度调整BELL格式块大小
2. **负载平衡**: 调整两个内核的矩阵分割比例
3. **内存对齐**: 使用128字节对齐优化内存访问
4. **Stream优化**: 考虑使用更多Stream进行流水线

### 调试技巧
1. **NVTX标记**: 使用Nsight Systems分析并行执行
2. **Stream同步**: 检查Stream之间的依赖关系
3. **内存检查**: 验证不同格式间的数据一致性
4. **精度验证**: 对比单精度和半精度的数值差异

## 扩展方向

### 1. 动态负载平衡
- 运行时调整两个内核的工作量分配
- 基于GPU利用率的自适应调度

### 2. 多GPU支持
- 跨GPU的混合并行执行
- 不同GPU架构的异构计算

### 3. 更多稀疏格式
- 集成更多稀疏格式 (COO, BSR等)
- 自适应格式选择

### 4. 精度优化
- 混合精度计算优化
- INT8量化支持

## 故障排除

### 常见问题
1. **编译错误**: 检查Sputnik和cuSPARSE库路径
2. **运行时错误**: 验证GPU架构支持Tensor Core
3. **性能异常**: 检查并行执行的Stream同步
4. **精度问题**: 验证half和float类型转换

### 调试命令
```bash
# 检查库依赖
ldd ./test_dual_bell_sputnik

# 性能分析
nsys profile ./test_dual_bell_sputnik

# 详细分析
ncu --set full ./test_dual_bell_sputnik
```

## 技术参考

1. [cuSPARSE BELL格式文档](https://docs.nvidia.com/cuda/cusparse/)
2. [Sputnik库文档](https://github.com/google-research/sputnik)
3. [CUDA Streams编程指南](https://docs.nvidia.com/cuda/cuda-c-programming-guide/)
4. [Tensor Core编程指南](https://docs.nvidia.com/cuda/tensor-core/)

## 贡献

本程序基于SpInfer项目，实现了BELL+Sputnik的混合并行架构，为稀疏矩阵计算提供了新的性能优化方案。
