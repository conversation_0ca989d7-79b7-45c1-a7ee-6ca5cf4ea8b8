/***************************************************************************
 * BELL+Sputnik混合并行测试程序 - 双内核Stream并行执行
 * 测试cuSPARSE BELL和Sputnik的混合并行执行和性能对比
 * 
 * 架构说明:
 * - 内核1: cuSPARSE BELL (块稀疏，60%稀疏度，Tensor Core优化)
 * - 内核2: Sputnik (高稀疏度，90%稀疏度，CUDA Core优化)
 * - 并行方式: CUDA Streams非阻塞并行执行
 ***************************************************************************/
#include <iostream>
#include <vector>
#include <cmath>
#include <cuda_fp16.h>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <cusparse.h>
#include <nvtx3/nvToolsExt.h>
#include "../kernel_benchmark/sputnik_utils.h"
#include "sputnik/sputnik.h"

// 性能测试常量
#define WARM_UP_ITERATION 2
#define BENCHMARK_ITERATION 5

// 错误检查宏
#define CHECK_CUDA_ERROR(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        exit(1); \
    } \
} while(0)

#define CHECK_CUSPARSE(call) do { \
    cusparseStatus_t st = call; \
    if (st != CUSPARSE_STATUS_SUCCESS) { \
        printf("cuSPARSE error at %s:%d - %d\n", __FILE__, __LINE__, (int)st); \
        exit(1); \
    } \
} while(0)

#define CHECK_CUBLAS(call) do { \
    cublasStatus_t st = call; \
    if (st != CUBLAS_STATUS_SUCCESS) { \
        printf("cuBLAS error at %s:%d - %d\n", __FILE__, __LINE__, (int)st); \
        exit(1); \
    } \
} while(0)

#define CUDA_CALL(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
        exit(1); \
    } \
} while(0)

// 性能对比工具函数
void PrintDualPerformance(const char* KernelName, float milliseconds, float tflops, double error = 0.0) {
    printf("%-20s -> Time: %6.3f ms, Performance: %6.2f TFLOPS",
           KernelName, milliseconds, tflops);
    if (error > 0.0) {
        printf(", Error: %.2e", error);
    }
    printf("\n");
}

double ComputeDualError(half* reference, half* result, int M, int N) {
    double totalError = 0.0;
    for (int i = 0; i < M * N; i++) {
        double diff = __half2float(reference[i]) - __half2float(result[i]);
        totalError += diff * diff;
    }
    return sqrt(totalError / (M * N));
}

// 生成BELL格式块稀疏矩阵 (60%稀疏度)
void generate_bell_data_60percent(half* matrix, int M, int K, int blockSize, 
                                 std::vector<int>& blockColIndices, 
                                 std::vector<half>& blockValues) {
    float sparsity = 0.6f;  // 60%稀疏度
    int mb = (M + blockSize - 1) / blockSize;
    int kb = (K + blockSize - 1) / blockSize;
    int maxBlocksPerRow = (int)((1.0f - sparsity) * kb) + 1;
    
    // cuSPARSE BELL格式要求：ellCols必须是blockSize的倍数
    maxBlocksPerRow = ((maxBlocksPerRow + blockSize - 1) / blockSize) * blockSize;
    
    printf("内核1 BELL格式: %d x %d 块, 块大小: %d x %d, 每行最大块数: %d\n", 
           mb, kb, blockSize, blockSize, maxBlocksPerRow);
    
    // 初始化矩阵为零
    memset(matrix, 0, M * K * sizeof(half));
    
    // 初始化BELL格式存储
    blockColIndices.resize(mb * maxBlocksPerRow, -1);
    blockValues.resize(mb * maxBlocksPerRow * blockSize * blockSize, __float2half(0.0f));
    
    // 为每个块行生成稀疏模式
    for (int brow = 0; brow < mb; brow++) {
        int numActiveBlocks = 0;
        
        for (int bcol = 0; bcol < kb && numActiveBlocks < maxBlocksPerRow; bcol++) {
            // 根据稀疏度决定是否为活跃块
            if ((float)rand() / RAND_MAX > sparsity) {
                // 记录块列索引
                blockColIndices[brow * maxBlocksPerRow + numActiveBlocks] = bcol;
                
                // 生成块内的随机值
                for (int bi = 0; bi < blockSize; bi++) {
                    for (int bj = 0; bj < blockSize; bj++) {
                        int row = brow * blockSize + bi;
                        int col = bcol * blockSize + bj;
                        
                        if (row < M && col < K) {
                            half value = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);
                            matrix[row * K + col] = value;
                            
                            // 存储到BELL格式（列主序）
                            size_t base = ((size_t)brow * maxBlocksPerRow + numActiveBlocks) *
                                         (size_t)blockSize * blockSize;
                            size_t blockValueIndex = base + (size_t)bj * blockSize + bi;  // 列主序
                            blockValues[blockValueIndex] = value;
                        }
                    }
                }
                numActiveBlocks++;
            }
        }
    }
    
    printf("内核1数据生成完成: BELL格式，%.1f%% 稀疏度\n", sparsity * 100.0f);
}

// 生成Sputnik CSR格式稀疏矩阵 (90%稀疏度) - 保持原有函数
void generate_sputnik_data_90percent(half* matrix, int M, int K, float sparsity,
                                    std::vector<int>& csr_row_ptr, 
                                    std::vector<int>& csr_col_indices, 
                                    std::vector<half>& csr_values) {
    printf("内核2 Sputnik格式: %d x %d 矩阵, %.1f%% 稀疏度\n", M, K, sparsity * 100.0f);
    
    // 初始化矩阵为零
    memset(matrix, 0, M * K * sizeof(half));
    
    // 初始化CSR格式
    csr_row_ptr.resize(M + 1, 0);
    csr_col_indices.clear();
    csr_values.clear();
    
    int nnz = 0;
    for (int i = 0; i < M; i++) {
        csr_row_ptr[i] = nnz;
        for (int j = 0; j < K; j++) {
            if ((float)rand() / RAND_MAX > sparsity) {
                half value = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);
                matrix[i * K + j] = value;
                
                csr_col_indices.push_back(j);
                csr_values.push_back(value);
                nnz++;
            }
        }
    }
    csr_row_ptr[M] = nnz;
    
    printf("内核2数据生成完成: CSR格式，实际非零元素: %d\n", nnz);
}

// 生成随机密集矩阵
void generate_dense_matrix(half* matrix, int rows, int cols) {
    for (int i = 0; i < rows * cols; i++) {
        matrix[i] = __float2half(((float)rand() / RAND_MAX - 0.5f) * 2.0f);
    }
}

// 矩阵维度 (与原程序保持一致)
const int M_GLOBAL = 18944;  // 稀疏矩阵A的行数
const int K_GLOBAL = 3584;   // 稀疏矩阵A的列数 / 密集矩阵B的行数
const int N_GLOBAL = 32;     // 密集矩阵B的列数

int main(int argc, char* argv[]) {
    printf("=== BELL+Sputnik混合并行测试程序 ===\n");
    printf("矩阵维度: A(%d x %d) × B(%d x %d) = C(%d x %d)\n", 
           M_GLOBAL, K_GLOBAL, K_GLOBAL, N_GLOBAL, M_GLOBAL, N_GLOBAL);
    printf("内核1: cuSPARSE BELL (块稀疏，60%%稀疏度，Tensor Core优化)\n");
    printf("内核2: Sputnik (高稀疏度，90%%稀疏度，CUDA Core优化)\n");
    printf("并行方式: CUDA Streams非阻塞并行执行\n\n");

    // ========== 步骤1: 数据准备 ==========
    printf("步骤1: 生成测试数据\n");
    
    // 内核1 (BELL) 数据结构
    int blockSize1 = 16;  // BELL格式块大小
    int mb1 = (M_GLOBAL + blockSize1 - 1) / blockSize1;
    int kb1 = (K_GLOBAL + blockSize1 - 1) / blockSize1;
    int maxBlocksPerRow1 = (int)((1.0f - 0.6f) * kb1) + 1;
    maxBlocksPerRow1 = ((maxBlocksPerRow1 + blockSize1 - 1) / blockSize1) * blockSize1;
    
    half* A1_h = new half[M_GLOBAL * K_GLOBAL];
    std::vector<int> blockColIndices1;
    std::vector<half> blockValues1;
    
    // 内核2 (Sputnik) 数据结构
    half* A2_h = new half[M_GLOBAL * K_GLOBAL];
    std::vector<int> csr_row_ptr2;
    std::vector<int> csr_col_indices2;
    std::vector<half> csr_values2;
    
    // 共享的密集矩阵B和结果矩阵C
    half* B_h = new half[K_GLOBAL * N_GLOBAL];
    half* C1_h = new half[M_GLOBAL * N_GLOBAL];  // 内核1结果
    half* C2_h = new half[M_GLOBAL * N_GLOBAL];  // 内核2结果
    half* C_parallel_h = new half[M_GLOBAL * N_GLOBAL];    // 并行结果
    half* C_sequential_h = new half[M_GLOBAL * N_GLOBAL];  // 顺序结果
    half* C_cublas1_h = new half[M_GLOBAL * N_GLOBAL];     // cuBLAS基准1
    half* C_cublas2_h = new half[M_GLOBAL * N_GLOBAL];     // cuBLAS基准2
    
    // 生成测试数据
    generate_bell_data_60percent(A1_h, M_GLOBAL, K_GLOBAL, blockSize1, blockColIndices1, blockValues1);
    generate_sputnik_data_90percent(A2_h, M_GLOBAL, K_GLOBAL, 0.9f, csr_row_ptr2, csr_col_indices2, csr_values2);
    generate_dense_matrix(B_h, K_GLOBAL, N_GLOBAL);
    
    // 初始化结果矩阵
    memset(C1_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    memset(C2_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    memset(C_parallel_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    memset(C_sequential_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    memset(C_cublas1_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    memset(C_cublas2_h, 0, M_GLOBAL * N_GLOBAL * sizeof(half));
    
    printf("数据生成完成\n");

    // ========== 步骤2: GPU内存分配 ==========
    printf("\n步骤2: GPU内存分配\n");
    
    // 内核1 (BELL) GPU内存
    int* d_blockColIndices1 = nullptr;
    half* d_blockValues1 = nullptr;
    half* d_B1 = nullptr;
    half* d_C1 = nullptr;
    half* d_A1_dense = nullptr;  // 用于cuBLAS基准
    
    CHECK_CUDA_ERROR(cudaMalloc(&d_blockColIndices1, blockColIndices1.size() * sizeof(int)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_blockValues1, blockValues1.size() * sizeof(half)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_B1, K_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_C1, M_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_A1_dense, M_GLOBAL * K_GLOBAL * sizeof(half)));
    
    // 内核2 (Sputnik) GPU内存
    int* d_csr_row_ptr2 = nullptr;
    int* d_csr_col_indices2 = nullptr;
    half* d_csr_values2 = nullptr;
    half* d_B2 = nullptr;
    half* d_C2 = nullptr;
    half* d_A2_dense = nullptr;  // 用于cuBLAS基准
    
    CHECK_CUDA_ERROR(cudaMalloc(&d_csr_row_ptr2, csr_row_ptr2.size() * sizeof(int)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_csr_col_indices2, csr_col_indices2.size() * sizeof(int)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_csr_values2, csr_values2.size() * sizeof(half)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_B2, K_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_C2, M_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_A2_dense, M_GLOBAL * K_GLOBAL * sizeof(half)));
    
    // 并行和顺序执行结果GPU内存
    half* d_C_parallel = nullptr;
    half* d_C_sequential = nullptr;
    
    CHECK_CUDA_ERROR(cudaMalloc(&d_C_parallel, M_GLOBAL * N_GLOBAL * sizeof(half)));
    CHECK_CUDA_ERROR(cudaMalloc(&d_C_sequential, M_GLOBAL * N_GLOBAL * sizeof(half)));
    
    printf("GPU内存分配完成\n");

    // ========== 步骤3: 数据传输到GPU ==========
    printf("\n步骤3: 数据传输到GPU\n");
    
    // 内核1数据传输
    CHECK_CUDA_ERROR(cudaMemcpy(d_blockColIndices1, blockColIndices1.data(), 
                                blockColIndices1.size() * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(d_blockValues1, blockValues1.data(), 
                                blockValues1.size() * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(d_B1, B_h, K_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(d_A1_dense, A1_h, M_GLOBAL * K_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    
    printf("内核1数据传输完成\n");
    
    // 内核2数据传输
    CHECK_CUDA_ERROR(cudaMemcpy(d_csr_row_ptr2, csr_row_ptr2.data(), 
                                csr_row_ptr2.size() * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(d_csr_col_indices2, csr_col_indices2.data(), 
                                csr_col_indices2.size() * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(d_csr_values2, csr_values2.data(), 
                                csr_values2.size() * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(d_B2, B_h, K_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    CHECK_CUDA_ERROR(cudaMemcpy(d_A2_dense, A2_h, M_GLOBAL * K_GLOBAL * sizeof(half), cudaMemcpyHostToDevice));
    
    printf("内核2数据传输完成\n");

    // ========== 步骤4: 初始化cuSPARSE和Sputnik ==========
    printf("\n步骤4: 初始化cuSPARSE和Sputnik\n");

    // 内核1: cuSPARSE BELL初始化
    cusparseHandle_t cusparse_handle1;
    CHECK_CUSPARSE(cusparseCreate(&cusparse_handle1));

    cusparseSpMatDescr_t matA1;
    cusparseDnMatDescr_t matB1, matC1;

    // 数据类型设置 (FP16启用Tensor Core)
    cudaDataType A_B_type = CUDA_R_16F;
    cudaDataType C_type = CUDA_R_16F;
    cudaDataType compute = CUDA_R_32F;

    // 创建BELL格式稀疏矩阵描述符
    CHECK_CUSPARSE(cusparseCreateBlockedEll(
        &matA1,
        M_GLOBAL, K_GLOBAL,
        blockSize1,
        maxBlocksPerRow1,
        d_blockColIndices1, d_blockValues1,
        CUSPARSE_INDEX_32I,
        CUSPARSE_INDEX_BASE_ZERO,
        A_B_type));

    // 创建密集矩阵描述符
    int ldb1 = N_GLOBAL, ldc1 = N_GLOBAL;
    CHECK_CUSPARSE(cusparseCreateDnMat(&matB1, K_GLOBAL, N_GLOBAL, ldb1, d_B1, A_B_type, CUSPARSE_ORDER_ROW));
    CHECK_CUSPARSE(cusparseCreateDnMat(&matC1, M_GLOBAL, N_GLOBAL, ldc1, d_C1, C_type, CUSPARSE_ORDER_ROW));

    // 查询缓冲区大小
    float alpha = 1.0f, beta = 0.0f;
    size_t bufferSize1 = 0;
    CHECK_CUSPARSE(cusparseSpMM_bufferSize(
        cusparse_handle1,
        CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
        &alpha, matA1, matB1, &beta, matC1, compute,
        CUSPARSE_SPMM_ALG_DEFAULT, &bufferSize1));

    void* d_bell_buffer1 = nullptr;
    CHECK_CUDA_ERROR(cudaMalloc(&d_bell_buffer1, bufferSize1));

    printf("内核1 cuSPARSE BELL初始化完成，缓冲区大小: %.2f MB\n", bufferSize1 / (1024.0f * 1024.0f));

    // 内核2: Sputnik初始化 (保持原有逻辑)
    printf("内核2 Sputnik初始化完成\n");

    // ========== 步骤5: 创建CUDA Streams ==========
    printf("\n步骤5: 创建非阻塞CUDA Streams用于混合并行执行\n");

    // 创建两个独立的非阻塞CUDA streams
    cudaStream_t stream1, stream2;
    CHECK_CUDA_ERROR(cudaStreamCreateWithFlags(&stream1, cudaStreamNonBlocking));
    CHECK_CUDA_ERROR(cudaStreamCreateWithFlags(&stream2, cudaStreamNonBlocking));

    // 创建CUDA事件用于计时
    cudaEvent_t start, stop;
    cudaEvent_t start1, stop1, start2, stop2;
    CHECK_CUDA_ERROR(cudaEventCreateWithFlags(&start, cudaEventDefault));
    CHECK_CUDA_ERROR(cudaEventCreateWithFlags(&stop, cudaEventDefault));
    CHECK_CUDA_ERROR(cudaEventCreateWithFlags(&start1, cudaEventDefault));
    CHECK_CUDA_ERROR(cudaEventCreateWithFlags(&stop1, cudaEventDefault));
    CHECK_CUDA_ERROR(cudaEventCreateWithFlags(&start2, cudaEventDefault));
    CHECK_CUDA_ERROR(cudaEventCreateWithFlags(&stop2, cudaEventDefault));

    printf("Stream1: 内核1 (cuSPARSE BELL) - 非阻塞流\n");
    printf("Stream2: 内核2 (Sputnik) - 非阻塞流\n");

    // ========== 步骤6: 单独内核性能测试 ==========
    printf("\n步骤6: 单独内核性能测试\n");

    // === 内核1单独测试（使用默认stream） ===
    printf("内核1 (cuSPARSE BELL) 单独性能测试（默认stream）...\n");
    // 预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        CHECK_CUSPARSE(cusparseSpMM(
            cusparse_handle1,
            CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
            &alpha, matA1, matB1, &beta, matC1, compute,
            CUSPARSE_SPMM_ALG_DEFAULT, d_bell_buffer1));
    }
    CHECK_CUDA_ERROR(cudaDeviceSynchronize());

    // 计时测试
    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        nvtxRangePushA("BELL SpMM iteration");
        CHECK_CUSPARSE(cusparseSpMM(
            cusparse_handle1,
            CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
            &alpha, matA1, matB1, &beta, matC1, compute,
            CUSPARSE_SPMM_ALG_DEFAULT, d_bell_buffer1));
        nvtxRangePop();
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float kernel1_time;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&kernel1_time, start, stop));
    kernel1_time /= BENCHMARK_ITERATION;

    // 拷贝结果
    CHECK_CUDA_ERROR(cudaMemcpy(C1_h, d_C1, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));

    printf("内核1单独测试完成，平均时间: %.3f ms\n", kernel1_time);

    // === 内核2单独测试（使用默认stream） ===
    printf("内核2 (Sputnik) 单独性能测试（默认stream）...\n");

    // 转换为float类型 (Sputnik需要)
    float* A2_float = new float[M_GLOBAL * K_GLOBAL];
    for (int i = 0; i < M_GLOBAL * K_GLOBAL; i++) {
        A2_float[i] = __half2float(A2_h[i]);
    }

    // 创建Sputnik稀疏矩阵
    sputnik_utils::SparseMatrix sparse_matrix2(M_GLOBAL, K_GLOBAL, A2_float,
                                               sputnik_utils::IDENTITY, 4);

    // 创建Sputnik GPU稀疏矩阵
    sputnik_utils::CudaSparseMatrix<half2> sparse_matrix2_gpu(sparse_matrix2);

    // 预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        nvtxRangePushA("Sputnik SpMM iteration");
        CUDA_CALL(sputnik::CudaSpmm(M_GLOBAL,
                                    K_GLOBAL,
                                    N_GLOBAL,
                                    sparse_matrix2_gpu.NumElementsWithPadding(),
                                    sparse_matrix2_gpu.RowIndices(),
                                    sparse_matrix2_gpu.Values(),
                                    sparse_matrix2_gpu.RowOffsets(),
                                    sparse_matrix2_gpu.ColumnIndices(),
                                    reinterpret_cast<half2*>(d_B2),
                                    reinterpret_cast<half2*>(d_C2),
                                    0));
        nvtxRangePop();
    }
    CHECK_CUDA_ERROR(cudaDeviceSynchronize());

    // 计时测试
    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        nvtxRangePushA("Sputnik SpMM iteration");
        CUDA_CALL(sputnik::CudaSpmm(M_GLOBAL,
                                    K_GLOBAL,
                                    N_GLOBAL,
                                    sparse_matrix2_gpu.NumElementsWithPadding(),
                                    sparse_matrix2_gpu.RowIndices(),
                                    sparse_matrix2_gpu.Values(),
                                    sparse_matrix2_gpu.RowOffsets(),
                                    sparse_matrix2_gpu.ColumnIndices(),
                                    reinterpret_cast<half2*>(d_B2),
                                    reinterpret_cast<half2*>(d_C2),
                                    0));
        nvtxRangePop();
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float kernel2_time;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&kernel2_time, start, stop));
    kernel2_time /= BENCHMARK_ITERATION;

    // 拷贝结果
    CHECK_CUDA_ERROR(cudaMemcpy(C2_h, d_C2, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));

    printf("内核2单独测试完成，平均时间: %.3f ms\n", kernel2_time);

    delete[] A2_float;

    // ========== 步骤7: 混合并行执行测试 ==========
    printf("\n步骤7: BELL+Sputnik混合并行执行测试\n");

    // 预热并行执行
    printf("预热并行执行...\n");
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        // 同时启动两个内核在不同的stream中
        nvtxRangePushA("BELL SpMM iteration");
        cusparseSetStream(cusparse_handle1, stream1);
        CHECK_CUSPARSE(cusparseSpMM(
            cusparse_handle1,
            CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
            &alpha, matA1, matB1, &beta, matC1, compute,
            CUSPARSE_SPMM_ALG_DEFAULT, d_bell_buffer1));
        nvtxRangePop();

        nvtxRangePushA("Sputnik SpMM iteration");
        CUDA_CALL(sputnik::CudaSpmm(M_GLOBAL,
                                    K_GLOBAL,
                                    N_GLOBAL,
                                    sparse_matrix2_gpu.NumElementsWithPadding(),
                                    sparse_matrix2_gpu.RowIndices(),
                                    sparse_matrix2_gpu.Values(),
                                    sparse_matrix2_gpu.RowOffsets(),
                                    sparse_matrix2_gpu.ColumnIndices(),
                                    reinterpret_cast<half2*>(d_B2),
                                    reinterpret_cast<half2*>(d_C2),
                                    stream2));
        nvtxRangePop();
        // 等待两个stream都完成
        CHECK_CUDA_ERROR(cudaStreamSynchronize(stream1));
        CHECK_CUDA_ERROR(cudaStreamSynchronize(stream2));
    }

    // 正式并行执行计时
    printf("正式并行执行计时...\n");
    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        // 记录每个stream的开始时间
        CHECK_CUDA_ERROR(cudaEventRecord(start1, stream1));
        CHECK_CUDA_ERROR(cudaEventRecord(start2, stream2));

        // 同时启动两个内核
        nvtxRangePushA("BELL SpMM iteration");
        cusparseSetStream(cusparse_handle1, stream1);
        CHECK_CUSPARSE(cusparseSpMM(
            cusparse_handle1,
            CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
            &alpha, matA1, matB1, &beta, matC1, compute,
            CUSPARSE_SPMM_ALG_DEFAULT, d_bell_buffer1));
        nvtxRangePop();

        nvtxRangePushA("Sputnik SpMM iteration");
        CUDA_CALL(sputnik::CudaSpmm(M_GLOBAL,
                                    K_GLOBAL,
                                    N_GLOBAL,
                                    sparse_matrix2_gpu.NumElementsWithPadding(),
                                    sparse_matrix2_gpu.RowIndices(),
                                    sparse_matrix2_gpu.Values(),
                                    sparse_matrix2_gpu.RowOffsets(),
                                    sparse_matrix2_gpu.ColumnIndices(),
                                    reinterpret_cast<half2*>(d_B2),
                                    reinterpret_cast<half2*>(d_C2),
                                    stream2));
        nvtxRangePop();

        // 记录每个stream的结束时间
        CHECK_CUDA_ERROR(cudaEventRecord(stop1, stream1));
        CHECK_CUDA_ERROR(cudaEventRecord(stop2, stream2));

        // 等待两个stream都完成
        CHECK_CUDA_ERROR(cudaStreamSynchronize(stream1));
        CHECK_CUDA_ERROR(cudaStreamSynchronize(stream2));
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float total_parallel_time;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&total_parallel_time, start, stop));
    total_parallel_time /= BENCHMARK_ITERATION;

    if (BENCHMARK_ITERATION == 1) {  // 只在单次执行时检查
        float stream1_time, stream2_time;
        CHECK_CUDA_ERROR(cudaEventElapsedTime(&stream1_time, start1, stop1));
        CHECK_CUDA_ERROR(cudaEventElapsedTime(&stream2_time, start2, stop2));
        printf("调试信息 - Stream1(BELL)执行时间: %.3f ms\n", stream1_time);
        printf("调试信息 - Stream2(Sputnik)执行时间: %.3f ms\n", stream2_time);
    }

    // 拷贝并行执行结果
    CHECK_CUDA_ERROR(cudaMemcpy(C_parallel_h, d_C1, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));

    printf("并行执行完成，总时间: %.3f ms\n", total_parallel_time);

    // ========== 步骤8: 顺序执行测试 ==========
    printf("\n步骤8: 顺序执行测试（用于对比）\n");

    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        // 先执行内核1
        nvtxRangePushA("BELL SpMM iteration");
        CHECK_CUSPARSE(cusparseSpMM(
            cusparse_handle1,
            CUSPARSE_OPERATION_NON_TRANSPOSE, CUSPARSE_OPERATION_NON_TRANSPOSE,
            &alpha, matA1, matB1, &beta, matC1, compute,
            CUSPARSE_SPMM_ALG_DEFAULT, d_bell_buffer1));
        nvtxRangePop();
        CHECK_CUDA_ERROR(cudaDeviceSynchronize());

        // 再执行内核2
        nvtxRangePushA("Sputnik SpMM iteration");
        CUDA_CALL(sputnik::CudaSpmm(M_GLOBAL,
                                    K_GLOBAL,
                                    N_GLOBAL,
                                    sparse_matrix2_gpu.NumElementsWithPadding(),
                                    sparse_matrix2_gpu.RowIndices(),
                                    sparse_matrix2_gpu.Values(),
                                    sparse_matrix2_gpu.RowOffsets(),
                                    sparse_matrix2_gpu.ColumnIndices(),
                                    reinterpret_cast<half2*>(d_B2),
                                    reinterpret_cast<half2*>(d_C2),
                                    0));
        nvtxRangePop();
        CHECK_CUDA_ERROR(cudaDeviceSynchronize());
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float total_sequential_time;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&total_sequential_time, start, stop));
    total_sequential_time /= BENCHMARK_ITERATION;

    // 拷贝顺序执行结果
    CHECK_CUDA_ERROR(cudaMemcpy(C_sequential_h, d_C1, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));

    printf("顺序执行完成，总时间: %.3f ms\n", total_sequential_time);

    // ========== 步骤9: cuBLAS基准测试 ==========
    printf("\n步骤9: cuBLAS基准测试\n");

    // 创建cuBLAS句柄
    cublasHandle_t cublasHandle;
    CHECK_CUBLAS(cublasCreate(&cublasHandle));
    CHECK_CUBLAS(cublasSetMathMode(cublasHandle, CUBLAS_DEFAULT_MATH));

    // 内核1 cuBLAS基准测试
    printf("内核1 cuBLAS基准测试...\n");
    CHECK_CUBLAS(cublasSetStream(cublasHandle, 0));

    // 预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        CHECK_CUBLAS(cublasGemmEx(cublasHandle, CUBLAS_OP_N, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, d_A1_dense, CUDA_R_16F, M_GLOBAL,
                     d_B1, CUDA_R_16F, K_GLOBAL,
                     &beta, d_C1, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0)));
    }
    CHECK_CUDA_ERROR(cudaDeviceSynchronize());

    // 计时
    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        CHECK_CUBLAS(cublasGemmEx(cublasHandle, CUBLAS_OP_N, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, d_A1_dense, CUDA_R_16F, M_GLOBAL,
                     d_B1, CUDA_R_16F, K_GLOBAL,
                     &beta, d_C1, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0)));
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float cublas1_time;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&cublas1_time, start, stop));
    cublas1_time /= BENCHMARK_ITERATION;

    // 拷贝cuBLAS结果
    CHECK_CUDA_ERROR(cudaMemcpy(C_cublas1_h, d_C1, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));

    printf("内核1 cuBLAS基准测试完成，时间: %.3f ms\n", cublas1_time);

    // 内核2 cuBLAS基准测试
    printf("内核2 cuBLAS基准测试...\n");

    // 预热
    for (int i = 0; i < WARM_UP_ITERATION; i++) {
        CHECK_CUBLAS(cublasGemmEx(cublasHandle, CUBLAS_OP_N, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, d_A2_dense, CUDA_R_16F, M_GLOBAL,
                     d_B2, CUDA_R_16F, K_GLOBAL,
                     &beta, d_C2, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0)));
    }
    CHECK_CUDA_ERROR(cudaDeviceSynchronize());

    // 计时
    CHECK_CUDA_ERROR(cudaEventRecord(start));
    for (int i = 0; i < BENCHMARK_ITERATION; i++) {
        CHECK_CUBLAS(cublasGemmEx(cublasHandle, CUBLAS_OP_N, CUBLAS_OP_N,
                     M_GLOBAL, N_GLOBAL, K_GLOBAL,
                     &alpha, d_A2_dense, CUDA_R_16F, M_GLOBAL,
                     d_B2, CUDA_R_16F, K_GLOBAL,
                     &beta, d_C2, CUDA_R_16F, M_GLOBAL,
                     CUDA_R_32F, static_cast<cublasGemmAlgo_t>(0)));
    }
    CHECK_CUDA_ERROR(cudaEventRecord(stop));
    CHECK_CUDA_ERROR(cudaEventSynchronize(stop));

    float cublas2_time;
    CHECK_CUDA_ERROR(cudaEventElapsedTime(&cublas2_time, start, stop));
    cublas2_time /= BENCHMARK_ITERATION;

    // 拷贝cuBLAS结果
    CHECK_CUDA_ERROR(cudaMemcpy(C_cublas2_h, d_C2, M_GLOBAL * N_GLOBAL * sizeof(half), cudaMemcpyDeviceToHost));

    printf("内核2 cuBLAS基准测试完成，时间: %.3f ms\n", cublas2_time);

    // ========== 步骤10: 性能分析和结果验证 ==========
    printf("\n步骤10: 性能分析和结果验证\n");

    // 计算TFLOPS
    double ops1 = 2.0 * M_GLOBAL * N_GLOBAL * K_GLOBAL * (1.0 - 0.6);  // 内核1: 60%稀疏度
    double ops2 = 2.0 * M_GLOBAL * N_GLOBAL * K_GLOBAL * (1.0 - 0.9);  // 内核2: 90%稀疏度
    double ops_total = ops1 + ops2;

    float tflops_kernel1 = (ops1 / (kernel1_time / 1000.0)) / 1e12;
    float tflops_kernel2 = (ops2 / (kernel2_time / 1000.0)) / 1e12;
    float tflops_parallel = (ops_total / (total_parallel_time / 1000.0)) / 1e12;
    float tflops_sequential = (ops_total / (total_sequential_time / 1000.0)) / 1e12;
    float tflops_cublas1 = (2.0 * M_GLOBAL * N_GLOBAL * K_GLOBAL / (cublas1_time / 1000.0)) / 1e12;
    float tflops_cublas2 = (2.0 * M_GLOBAL * N_GLOBAL * K_GLOBAL / (cublas2_time / 1000.0)) / 1e12;

    // 计算误差
    double error1 = ComputeDualError(C_cublas1_h, C1_h, M_GLOBAL, N_GLOBAL);
    double error2 = ComputeDualError(C_cublas2_h, C2_h, M_GLOBAL, N_GLOBAL);
    double error_parallel = ComputeDualError(C_cublas1_h, C_parallel_h, M_GLOBAL, N_GLOBAL);
    double error_sequential = ComputeDualError(C_cublas1_h, C_sequential_h, M_GLOBAL, N_GLOBAL);

    printf("\n=== 单独性能对比 ===\n");
    printf("内核1 (cuSPARSE BELL):\n");
    printf("  cuBLAS 基准      -> 时间: %6.3f ms, 性能: %6.2f TFLOPS\n", cublas1_time, tflops_cublas1);
    printf("  BELL SpMM        -> 时间: %6.3f ms, 性能: %6.2f TFLOPS, 加速比: %.2fx\n",
           kernel1_time, tflops_kernel1, cublas1_time / kernel1_time);

    printf("\n内核2 (Sputnik):\n");
    printf("  cuBLAS 基准      -> 时间: %6.3f ms, 性能: %6.2f TFLOPS\n", cublas2_time, tflops_cublas2);
    printf("  Sputnik SpMM     -> 时间: %6.3f ms, 性能: %6.2f TFLOPS, 加速比: %.2fx\n",
           kernel2_time, tflops_kernel2, cublas2_time / kernel2_time);

    printf("\n=== 混合并行性能分析 ===\n");
    printf("理论最佳时间 (max(BELL,Sputnik)): %.3f ms\n", fmax(kernel1_time, kernel2_time));
    printf("BELL+Sputnik 实际并行执行: %.3f ms, 性能: %.2f TFLOPS\n", total_parallel_time, tflops_parallel);
    printf("BELL+Sputnik 顺序执行: %.3f ms, 性能: %.2f TFLOPS\n", total_sequential_time, tflops_sequential);

    float parallel_efficiency = total_parallel_time / fmax(kernel1_time, kernel2_time);
    float parallel_speedup = total_sequential_time / total_parallel_time;

    printf("并行效率: %.1f%% (1.0为理想并行)\n", parallel_efficiency * 100.0f);
    printf("并行加速比: %.2fx\n", parallel_speedup);

    // 与cuBLAS总体对比
    float total_cublas_time = cublas1_time + cublas2_time;
    float total_cublas_tflops = (2.0 * M_GLOBAL * N_GLOBAL * K_GLOBAL * 2 / (total_cublas_time / 1000.0)) / 1e12;
    printf("cuBLAS总体基准: %.3f ms, 性能: %.2f TFLOPS\n", total_cublas_time, total_cublas_tflops);
    printf("混合并行 vs cuBLAS总体加速比: %.2fx\n", total_cublas_time / total_parallel_time);

    printf("\n=== 结果验证 ===\n");
    printf("内核1 (cuSPARSE BELL):\n");
    printf("  相对误差: ");
    if (std::isnan(error1) || std::isinf(error1)) {
        printf("NaN/Inf (可能的数值问题)\n");
    } else if (error1 < 1e-3) {
        printf("%.2e (良好)\n", error1);
    } else if (error1 < 1e-1) {
        printf("%.2e (可接受)\n", error1);
    } else {
        printf("%.2e (较大误差，需检查)\n", error1);
    }

    printf("内核2 (Sputnik):\n");
    printf("  相对误差: ");
    if (std::isnan(error2) || std::isinf(error2)) {
        printf("NaN/Inf (可能的数值问题)\n");
    } else if (error2 < 1e-3) {
        printf("%.2e (良好)\n", error2);
    } else if (error2 < 1e-1) {
        printf("%.2e (可接受)\n", error2);
    } else {
        printf("%.2e (较大误差，需检查)\n", error2);
    }

    printf("并行执行结果验证: ");
    if (std::isnan(error_parallel) || std::isinf(error_parallel)) {
        printf("NaN/Inf (可能的数值问题)\n");
    } else if (error_parallel < 1e-3) {
        printf("%.2e (良好)\n", error_parallel);
    } else if (error_parallel < 1e-1) {
        printf("%.2e (可接受)\n", error_parallel);
    } else {
        printf("%.2e (较大误差，需检查)\n", error_parallel);
    }

    // ========== 资源清理 ==========
    printf("\n清理GPU资源...\n");

    // 清理cuSPARSE资源
    CHECK_CUSPARSE(cusparseDestroySpMat(matA1));
    CHECK_CUSPARSE(cusparseDestroyDnMat(matB1));
    CHECK_CUSPARSE(cusparseDestroyDnMat(matC1));
    CHECK_CUSPARSE(cusparseDestroy(cusparse_handle1));

    // 清理cuBLAS资源
    CHECK_CUBLAS(cublasDestroy(cublasHandle));

    // 清理CUDA资源
    CHECK_CUDA_ERROR(cudaStreamDestroy(stream1));
    CHECK_CUDA_ERROR(cudaStreamDestroy(stream2));
    CHECK_CUDA_ERROR(cudaEventDestroy(start));
    CHECK_CUDA_ERROR(cudaEventDestroy(stop));
    CHECK_CUDA_ERROR(cudaEventDestroy(start1));
    CHECK_CUDA_ERROR(cudaEventDestroy(stop1));
    CHECK_CUDA_ERROR(cudaEventDestroy(start2));
    CHECK_CUDA_ERROR(cudaEventDestroy(stop2));

    // 清理GPU内存
    CHECK_CUDA_ERROR(cudaFree(d_blockColIndices1));
    CHECK_CUDA_ERROR(cudaFree(d_blockValues1));
    CHECK_CUDA_ERROR(cudaFree(d_B1));
    CHECK_CUDA_ERROR(cudaFree(d_C1));
    CHECK_CUDA_ERROR(cudaFree(d_A1_dense));
    CHECK_CUDA_ERROR(cudaFree(d_bell_buffer1));

    CHECK_CUDA_ERROR(cudaFree(d_csr_row_ptr2));
    CHECK_CUDA_ERROR(cudaFree(d_csr_col_indices2));
    CHECK_CUDA_ERROR(cudaFree(d_csr_values2));
    CHECK_CUDA_ERROR(cudaFree(d_B2));
    CHECK_CUDA_ERROR(cudaFree(d_C2));
    CHECK_CUDA_ERROR(cudaFree(d_A2_dense));

    CHECK_CUDA_ERROR(cudaFree(d_C_parallel));
    CHECK_CUDA_ERROR(cudaFree(d_C_sequential));

    // 清理CPU内存
    delete[] A1_h;
    delete[] A2_h;
    delete[] B_h;
    delete[] C1_h;
    delete[] C2_h;
    delete[] C_parallel_h;
    delete[] C_sequential_h;
    delete[] C_cublas1_h;
    delete[] C_cublas2_h;

    printf("=== BELL+Sputnik混合并行测试完成 ===\n");
    return 0;
}
