# SpInfer 三矩阵专用kernel测试 Makefile
# 使用方法: make -f Makefile_three_kernels

# 编译器设置
HOST_COMPILER ?= g++
CUDA_PATH ?= /usr/local/cuda
NVCC := $(CUDA_PATH)/bin/nvcc -ccbin $(HOST_COMPILER)

# 编译标志
NVCCFLAGS := -m$(shell getconf LONG_BIT)
CCFLAGS := -fPIC
LDFLAGS :=

ALL_CCFLAGS :=
ALL_CCFLAGS += $(NVCCFLAGS)
ALL_CCFLAGS += $(addprefix -Xcompiler ,$(CCFLAGS))

ALL_LDFLAGS :=
ALL_LDFLAGS += $(ALL_CCFLAGS)
ALL_LDFLAGS += $(addprefix -Xlinker ,$(LDFLAGS))

# 包含路径和库
INCLUDES := -I/usr/local/cuda/include/ -I./build/ -I./csrc/ -I./kernel_benchmark/ -I./third_party/sputnik/
LIBRARIES := -lcublas -lcusparse -L./third_party/sputnik/build/sputnik/ -lsputnik -L./third_party/glog/build/lib/ -lglog

# GPU架构
SMS ?= 86
$(foreach sm,$(SMS),$(eval GENCODE_FLAGS += -gencode arch=compute_$(sm),code=sm_$(sm)))

# 编译选项
ALL_CCFLAGS += --threads 0 --std=c++11
ALL_CCFLAGS += -maxrregcount=255 
ALL_CCFLAGS += --use_fast_math
ALL_CCFLAGS += --ptxas-options=-v,-warn-lmem-usage,--warn-on-spills

# 头文件依赖
HEAD_FILES_MATRIX1 = build/SpMM_API.cuh \
                     csrc/Reduction_Kernel.cuh csrc/SpMM_Kernel_Matrix1.cuh \
                     csrc/MatMulUtilities.cuh \
                     csrc/MMA_PTX.cuh csrc/AsyncCopy_PTX.cuh \
                     csrc/TilingConfig.h

HEAD_FILES_MATRIX2 = build/SpMM_API.cuh \
                     csrc/Reduction_Kernel.cuh csrc/SpMM_Kernel_Matrix2.cuh \
                     csrc/MatMulUtilities.cuh \
                     csrc/MMA_PTX.cuh csrc/AsyncCopy_PTX.cuh \
                     csrc/TilingConfig.h

HEAD_FILES_MATRIX3 = build/SpMM_API.cuh \
                     csrc/Reduction_Kernel.cuh csrc/SpMM_Kernel_Matrix3.cuh \
                     csrc/MatMulUtilities.cuh \
                     csrc/MMA_PTX.cuh csrc/AsyncCopy_PTX.cuh \
                     csrc/TilingConfig.h

# 目标规则
all: test_matrix1 test_matrix2 test_matrix3 test_dual_kernels test_sputnik

# 矩阵1测试程序（块稀疏）
test_matrix1: hybird_bench/test_matrix1.o build/libSpMM_API_Matrix1.so
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< -L./build -lSpMM_API_Matrix1 $(LIBRARIES) -Xlinker -rpath=./build

hybird_bench/test_matrix1.o: hybird_bench/test_matrix1.cu $(HEAD_FILES_MATRIX1)
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

# 矩阵2测试程序（稀疏块）
test_matrix2: hybird_bench/test_matrix2.o build/libSpMM_API_Matrix2.so
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< -L./build -lSpMM_API_Matrix2 $(LIBRARIES) -Xlinker -rpath=./build

hybird_bench/test_matrix2.o: hybird_bench/test_matrix2.cu $(HEAD_FILES_MATRIX2)
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

# 矩阵3测试程序（组合模式）
test_matrix3: hybird_bench/test_matrix3.o build/libSpMM_API_Matrix3.so
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< -L./build -lSpMM_API_Matrix3 $(LIBRARIES) -Xlinker -rpath=./build

hybird_bench/test_matrix3.o: hybird_bench/test_matrix3.cu $(HEAD_FILES_MATRIX3)
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

# 双内核测试程序（顺序执行）
test_dual_kernels: hybird_bench/test_dual_kernels.o build/libSpMM_API_Matrix1.so build/libSpMM_API_Matrix2.so
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< -L./build -lSpMM_API_Matrix1 -lSpMM_API_Matrix2 $(LIBRARIES) -Xlinker -rpath=./build

hybird_bench/test_dual_kernels.o: hybird_bench/test_dual_kernels.cu $(HEAD_FILES_MATRIX1) $(HEAD_FILES_MATRIX2)
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

# Sputnik测试程序（高稀疏度）
test_sputnik: hybird_bench/test_sputnik.o
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< $(LIBRARIES) -Xlinker -rpath=./third_party/glog/build/lib -Xlinker -rpath=./third_party/sputnik/build/sputnik

# TC+Sputnik混合并行测试程序
test_dual_sputnik: hybird_bench/test_dual_sputnik.o
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< build/SpMM_API_Matrix1.o $(LIBRARIES) -Xlinker -rpath=./third_party/glog/build/lib -Xlinker -rpath=./third_party/sputnik/build/sputnik

# cuSPARSE BELL格式SPMM测试程序
test_bell_spmm: hybird_bench/test_bell_spmm.o
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< $(LIBRARIES)

# BELL+Sputnik混合并行测试程序
test_dual_bell_sputnik: hybird_bench/test_dual_bell_sputnik.o
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< $(LIBRARIES) -Xlinker -rpath=./third_party/glog/build/lib -Xlinker -rpath=./third_party/sputnik/build/sputnik

# NVIDIA官方BELL格式示例测试程序
test_bell_nv: hybird_bench/test_bell_nv.o
	$(EXEC) $(NVCC) $(ALL_LDFLAGS) $(GENCODE_FLAGS) -o $@ $< $(LIBRARIES)

hybird_bench/test_sputnik.o: hybird_bench/test_sputnik.cu
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

hybird_bench/test_dual_sputnik.o: hybird_bench/test_dual_sputnik.cu
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

hybird_bench/test_bell_spmm.o: hybird_bench/test_bell_spmm.cu
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

hybird_bench/test_dual_bell_sputnik.o: hybird_bench/test_dual_bell_sputnik.cu
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

hybird_bench/test_bell_nv.o: hybird_bench/test_bell_nv.cu
	$(EXEC) $(NVCC) $(INCLUDES) $(ALL_CCFLAGS) $(GENCODE_FLAGS) -o $@ -c $<

# 确保SpMM库已编译
build/libSpMM_API_Matrix1.so:
	@echo "正在编译矩阵1专用库..."
	cd build && make libSpMM_API_Matrix1.so

build/libSpMM_API_Matrix2.so:
	@echo "正在编译矩阵2专用库..."
	cd build && make libSpMM_API_Matrix2.so

build/libSpMM_API_Matrix3.so:
	@echo "正在编译矩阵3专用库..."
	cd build && make libSpMM_API_Matrix3.so

# 清理
clean:
	rm -f test_matrix1 test_matrix2 test_matrix3 test_dual_kernels test_sputnik test_dual_sputnik test_bell_spmm test_dual_bell_sputnik test_bell_nv \
		  hybird_bench/test_matrix1.o hybird_bench/test_matrix2.o hybird_bench/test_matrix3.o hybird_bench/test_dual_kernels.o hybird_bench/test_sputnik.o hybird_bench/test_dual_sputnik.o hybird_bench/test_bell_spmm.o hybird_bench/test_dual_bell_sputnik.o hybird_bench/test_bell_nv.o

# 运行测试
run_matrix1: test_matrix1
	@echo "运行矩阵1测试（块稀疏）..."
	./test_matrix1 18944 3584 32

run_matrix2: test_matrix2
	@echo "运行矩阵2测试（稀疏块）..."
	./test_matrix2 18944 3584 32

run_matrix3: test_matrix3
	@echo "运行矩阵3测试（组合）..."
	./test_matrix3 18944 3584 32

run_dual_kernels: test_dual_kernels
	@echo "运行双内核测试（顺序执行）..."
	./test_dual_kernels

run_sputnik: test_sputnik
	@echo "运行Sputnik测试（高稀疏度）..."
	LD_LIBRARY_PATH=./third_party/glog/build/lib:./third_party/sputnik/build/sputnik:$$LD_LIBRARY_PATH ./test_sputnik 18944 3584 32

run_dual_sputnik: test_dual_sputnik
	@echo "运行TC+Sputnik混合并行测试..."
	LD_LIBRARY_PATH=./third_party/glog/build/lib:./third_party/sputnik/build/sputnik:$$LD_LIBRARY_PATH ./test_dual_sputnik

run_bell_spmm: test_bell_spmm
	@echo "运行cuSPARSE BELL格式SPMM测试..."
	./test_bell_spmm

run_dual_bell_sputnik: test_dual_bell_sputnik
	@echo "运行BELL+Sputnik混合并行测试..."
	LD_LIBRARY_PATH=./third_party/glog/build/lib:./third_party/sputnik/build/sputnik:$$LD_LIBRARY_PATH ./test_dual_bell_sputnik

run_bell_nv: test_bell_nv
	@echo "运行NVIDIA官方BELL格式示例测试..."
	./test_bell_nv


# ncu分析 bell sputnik
run_dual_bell_sputnik_ncu: run_dual_bell_sputnik
	@echo "使用NCU进行详细分析双内核测试..."
	@mkdir -p ncu
	ncu --set full --target-processes all \
		--export ncu/dual_bell_sputnik_detailed_analysis \
		--force-overwrite \
		--print-summary per-kernel \
		./test_dual_bell_sputnik

# 详细的nsys分析
run_dual_bell_sputnik_nsys: run_dual_bell_sputnik
	@echo "使用Nsys进行详细timeline分析..."
	@mkdir -p ncu
	nsys profile \
		--output=ncu/dual_bell_sputnik_detailed_timeline \
		--force-overwrite=true \
		--trace=cuda,nvtx,cublas,cusparse \
		--cuda-memory-usage=true \
		--gpu-metrics-device=all \
		./test_dual_bell_sputnik

# ncu分析
run_dual_kernels_ncu: test_dual_kernels
	@echo "使用NCU进行详细分析双内核测试..."
	@mkdir -p ncu
	ncu --set full --target-processes all \
		--export ncu/dual_kernels_detailed_analysis \
		--force-overwrite \
		--print-summary per-kernel \
		./test_dual_kernels

# 详细的nsys分析
run_dual_kernels_nsys: test_dual_kernels
	@echo "使用Nsys进行详细timeline分析..."
	@mkdir -p ncu
	nsys profile \
		--output=ncu/dual_kernels_detailed_timeline \
		--force-overwrite=true \
		--trace=cuda,nvtx,cublas,cusparse \
		--cuda-memory-usage=true \
		--gpu-metrics-device=all \
		./test_dual_kernels

# 详细的nsys分析 sputnik
run_dual_sputnik_nsys: test_dual_sputnik
	@echo "使用Nsys进行详细timeline分析..."
	@mkdir -p ncu
	nsys profile \
		--output=ncu/dual_sputnik_detailed_timeline \
		--force-overwrite=true \
		--trace=cuda,nvtx,cublas,cusparse \
		--cuda-memory-usage=true \
		--gpu-metrics-device=all \
		./test_dual_sputnik

# 详细的ncu分析 sputnik
run_dual_sputnik_ncu: test_dual_sputnik
	@echo "使用NCU进行详细分析双内核测试..."
	@mkdir -p ncu
	ncu --set full --target-processes all \
		--export ncu/dual_sputnik_detailed_analysis \
		--force-overwrite \
		--print-summary per-kernel \
		./test_dual_sputnik


run_all: test_matrix1 test_matrix2 test_matrix3 test_dual_kernels
	@echo "运行所有测试程序..."
	@echo "=== 矩阵1测试 ==="
	./test_matrix1 18944 3584 32
	@echo "=== 矩阵2测试 ==="
	./test_matrix2 18944 3584 32
	@echo "=== 矩阵3测试 ==="
	./test_matrix3 18944 3584 32
	@echo "=== 双内核测试 ==="
	./test_dual_kernels

# 运行所有测试
run_all: test_matrix1 test_matrix2 test_matrix3
	@echo "运行所有三矩阵测试..."
	@echo "\n=== 矩阵1测试 ==="
	./test_matrix1 3584 18944 256
	@echo "\n=== 矩阵2测试 ==="
	./test_matrix2 3584 18944 256
	@echo "\n=== 矩阵3测试 ==="
	./test_matrix3 3584 18944 256

# 性能对比测试
benchmark: test_matrix1 test_matrix2 test_matrix3
	@echo "性能对比测试..."
	@echo "矩阵1（块稀疏）:"
	./test_matrix1 3584 18944 256 | grep "Performance"
	@echo "矩阵2（稀疏块）:"
	./test_matrix2 3584 18944 256 | grep "Performance"
	@echo "矩阵3（组合）:"
	./test_matrix3 3584 18944 256 | grep "Performance"

clean:
	rm -f test_matrix1 test_matrix2 test_dual_kernels test_sputnik test_dual_sputnik
	rm -f hybird_bench/*.o

.PHONY: all clean run_matrix1 run_matrix2 run_matrix3 run_dual_kernels run_sputnik run_dual_sputnik run_all benchmark
