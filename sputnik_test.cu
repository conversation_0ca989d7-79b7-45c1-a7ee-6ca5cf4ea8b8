#include <cuda_runtime.h>
#include <cuda_fp16.h>
#include <iostream>
#include <vector>
#include <chrono>
#include <iomanip>
#include <random>

// 包含Sputnik头文件
#include "SpInfer/third_party/sputnik/sputnik/spmm/cuda_spmm.h"
// #include "SpInfer/third_party/sputnik/sputnik/matrix_utils.h"  // 暂时注释掉，避免abseil依赖

// 错误检查宏
#define CHECK_CUDA(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        std::cerr << "CUDA error at " << __FILE__ << ":" << __LINE__ << " - " << cudaGetErrorString(err) << std::endl; \
        exit(1); \
    } \
} while(0)

class SputnikSpmmTest {
private:
    int M, N, K;
    float sparsity;
    
    // CPU数据 - CSR格式
    std::vector<float> A_values;
    std::vector<int> A_row_offsets;
    std::vector<int> A_column_indices;
    std::vector<int> A_row_indices;  // Sputnik需要的行重排序
    std::vector<float> B_dense;
    std::vector<float> C_output;
    std::vector<float> C_reference;  // 用于验证的参考结果
    
    // GPU数据
    float *A_values_gpu;
    int *A_row_offsets_gpu;
    int *A_column_indices_gpu;
    int *A_row_indices_gpu;
    float *B_dense_gpu;
    float *C_output_gpu;
    
    int nonzeros;
    cudaEvent_t start, stop;
    cudaStream_t stream;
    
public:
    SputnikSpmmTest(int m, int n, int k, float sparsity_ratio = 0.9f) 
        : M(m), N(n), K(k), sparsity(sparsity_ratio) {
        
        // 创建CUDA事件和流
        CHECK_CUDA(cudaEventCreate(&start));
        CHECK_CUDA(cudaEventCreate(&stop));
        CHECK_CUDA(cudaStreamCreate(&stream));
        
        std::cout << "=== Sputnik SPMM 测试初始化 ===" << std::endl;
        std::cout << "矩阵大小: A(" << M << "x" << K << ") × B(" << K << "x" << N << ") = C(" << M << "x" << N << ")" << std::endl;
        std::cout << "目标稀疏度: " << sparsity * 100 << "%" << std::endl;
        
        generateSparseMatrix();
        allocateMemory();
        copyToGPU();
    }
    
    ~SputnikSpmmTest() {
        // 清理GPU内存
        if (A_values_gpu) cudaFree(A_values_gpu);
        if (A_row_offsets_gpu) cudaFree(A_row_offsets_gpu);
        if (A_column_indices_gpu) cudaFree(A_column_indices_gpu);
        if (A_row_indices_gpu) cudaFree(A_row_indices_gpu);
        if (B_dense_gpu) cudaFree(B_dense_gpu);
        if (C_output_gpu) cudaFree(C_output_gpu);
        
        cudaEventDestroy(start);
        cudaEventDestroy(stop);
        cudaStreamDestroy(stream);
    }
    
private:
    void generateSparseMatrix() {
        std::random_device rd;
        std::mt19937 gen(42);  // 固定种子以便重现
        std::uniform_real_distribution<float> dis(0.0f, 1.0f);
        std::uniform_real_distribution<float> val_dis(-1.0f, 1.0f);
        
        std::cout << "生成稀疏矩阵A (CSR格式)..." << std::endl;
        
        // 生成稀疏矩阵A (CSR格式)
        A_row_offsets.resize(M + 1);
        A_row_offsets[0] = 0;
        
        // 临时存储，用于生成参考结果
        std::vector<std::vector<std::pair<int, float>>> temp_matrix(M);
        
        for (int i = 0; i < M; i++) {
            for (int j = 0; j < K; j++) {
                if (dis(gen) > sparsity) {  // 非零元素
                    float value = val_dis(gen);
                    A_column_indices.push_back(j);
                    A_values.push_back(value);
                    temp_matrix[i].push_back({j, value});
                }
            }
            A_row_offsets[i + 1] = A_values.size();
        }
        
        nonzeros = A_values.size();
        
        // 生成行索引重排序 (简化版本，直接使用顺序索引)
        A_row_indices.resize(M);
        for (int i = 0; i < M; i++) {
            A_row_indices[i] = i;
        }
        
        std::cout << "生成密集矩阵B..." << std::endl;
        // 生成密集矩阵B
        B_dense.resize(K * N);
        for (int i = 0; i < K * N; i++) {
            B_dense[i] = val_dis(gen);
        }
        
        // 初始化输出矩阵C
        C_output.resize(M * N, 0.0f);
        
        // 计算参考结果用于验证
        std::cout << "计算参考结果..." << std::endl;
        computeReference(temp_matrix);
        
        float actual_sparsity = 1.0f - (float)nonzeros / (M * K);
        std::cout << "稀疏矩阵生成完成:" << std::endl;
        std::cout << "  非零元素数量: " << nonzeros << " / " << (M * K) << std::endl;
        std::cout << "  实际稀疏度: " << std::fixed << std::setprecision(1) << actual_sparsity * 100 << "%" << std::endl;
    }
    
    void computeReference(const std::vector<std::vector<std::pair<int, float>>>& temp_matrix) {
        C_reference.resize(M * N, 0.0f);
        
        for (int i = 0; i < M; i++) {
            for (int j = 0; j < N; j++) {
                float sum = 0.0f;
                for (const auto& elem : temp_matrix[i]) {
                    int k = elem.first;
                    float a_val = elem.second;
                    sum += a_val * B_dense[k * N + j];
                }
                C_reference[i * N + j] = sum;
            }
        }
    }
    
    void allocateMemory() {
        std::cout << "分配GPU内存..." << std::endl;
        
        // 分配GPU内存
        CHECK_CUDA(cudaMalloc(&A_values_gpu, nonzeros * sizeof(float)));
        CHECK_CUDA(cudaMalloc(&A_row_offsets_gpu, (M + 1) * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&A_column_indices_gpu, nonzeros * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&A_row_indices_gpu, M * sizeof(int)));
        CHECK_CUDA(cudaMalloc(&B_dense_gpu, K * N * sizeof(float)));
        CHECK_CUDA(cudaMalloc(&C_output_gpu, M * N * sizeof(float)));
        
        std::cout << "GPU内存分配完成" << std::endl;
    }
    
    void copyToGPU() {
        std::cout << "拷贝数据到GPU..." << std::endl;
        
        // 拷贝数据到GPU
        CHECK_CUDA(cudaMemcpy(A_values_gpu, A_values.data(), nonzeros * sizeof(float), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A_row_offsets_gpu, A_row_offsets.data(), (M + 1) * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A_column_indices_gpu, A_column_indices.data(), nonzeros * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(A_row_indices_gpu, A_row_indices.data(), M * sizeof(int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(B_dense_gpu, B_dense.data(), K * N * sizeof(float), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemset(C_output_gpu, 0, M * N * sizeof(float)));
        
        std::cout << "数据拷贝完成" << std::endl;
    }
    
public:
    bool runSputnikTest() {
        std::cout << "\n=== 开始 Sputnik SPMM 功能测试 ===" << std::endl;
        
        // 调用Sputnik SPMM
        cudaError_t result = sputnik::CudaSpmm(
            M, K, N, nonzeros,
            A_row_indices_gpu,
            A_values_gpu,
            A_row_offsets_gpu,
            A_column_indices_gpu,
            B_dense_gpu,
            C_output_gpu,
            stream
        );
        
        if (result != cudaSuccess) {
            std::cerr << "Sputnik SPMM 调用失败: " << cudaGetErrorString(result) << std::endl;
            return false;
        }
        
        CHECK_CUDA(cudaStreamSynchronize(stream));
        
        // 拷贝结果回CPU
        CHECK_CUDA(cudaMemcpy(C_output.data(), C_output_gpu, M * N * sizeof(float), cudaMemcpyDeviceToHost));
        
        // 验证结果
        return verifyResults();
    }
    
    void runBenchmark() {
        std::cout << "\n=== 开始 Sputnik SPMM 性能测试 ===" << std::endl;
        
        const int warm_up_iterations = 10;
        const int benchmark_iterations = 100;
        
        // 预热
        std::cout << "预热中..." << std::endl;
        for (int i = 0; i < warm_up_iterations; i++) {
            sputnik::CudaSpmm(M, K, N, nonzeros, A_row_indices_gpu, A_values_gpu,
                             A_row_offsets_gpu, A_column_indices_gpu, B_dense_gpu, C_output_gpu, stream);
        }
        CHECK_CUDA(cudaStreamSynchronize(stream));
        
        // 性能测试
        std::cout << "开始性能测试 (" << benchmark_iterations << " 次迭代)..." << std::endl;
        CHECK_CUDA(cudaEventRecord(start, stream));
        
        for (int i = 0; i < benchmark_iterations; i++) {
            sputnik::CudaSpmm(M, K, N, nonzeros, A_row_indices_gpu, A_values_gpu,
                             A_row_offsets_gpu, A_column_indices_gpu, B_dense_gpu, C_output_gpu, stream);
        }
        
        CHECK_CUDA(cudaEventRecord(stop, stream));
        CHECK_CUDA(cudaEventSynchronize(stop));
        
        // 计算性能
        float elapsed_time;
        CHECK_CUDA(cudaEventElapsedTime(&elapsed_time, start, stop));
        
        float avg_time = elapsed_time / benchmark_iterations;
        double flops = 2.0 * nonzeros * N;  // 稀疏矩阵的有效FLOP数
        double gflops = (flops / (avg_time * 1e-3)) / 1e9;
        
        // 输出结果
        printResults(avg_time, gflops);
    }
    
private:
    bool verifyResults() {
        std::cout << "验证计算结果..." << std::endl;

        float max_error = 0.0f;
        float avg_error = 0.0f;
        int error_count = 0;
        const float tolerance = 1e-4f;

        for (int i = 0; i < M * N; i++) {
            float error = std::abs(C_output[i] - C_reference[i]);
            max_error = std::max(max_error, error);
            avg_error += error;

            if (error > tolerance) {
                error_count++;
                if (error_count <= 10) {  // 只打印前10个错误
                    int row = i / N;
                    int col = i % N;
                    std::cout << "  错误 [" << row << "," << col << "]: 期望=" << C_reference[i]
                              << ", 实际=" << C_output[i] << ", 误差=" << error << std::endl;
                }
            }
        }

        avg_error /= (M * N);

        std::cout << "验证结果:" << std::endl;
        std::cout << "  最大误差: " << std::scientific << max_error << std::endl;
        std::cout << "  平均误差: " << std::scientific << avg_error << std::endl;
        std::cout << "  错误元素数量: " << error_count << " / " << (M * N) << std::endl;

        bool passed = (max_error < tolerance);
        std::cout << "  测试结果: " << (passed ? "通过" : "失败") << std::endl;

        return passed;
    }

    void printResults(float time_ms, double gflops) {
        std::cout << "\n========== Sputnik SPMM 性能测试结果 ==========" << std::endl;
        std::cout << "矩阵大小: " << M << " x " << K << " x " << N << std::endl;
        std::cout << "非零元素: " << nonzeros << " / " << (M * K) << std::endl;
        std::cout << "稀疏度: " << std::fixed << std::setprecision(1)
                  << (1.0f - (float)nonzeros / (M * K)) * 100 << "%" << std::endl;
        std::cout << "平均时间: " << std::fixed << std::setprecision(3) << time_ms << " ms" << std::endl;
        std::cout << "性能: " << std::fixed << std::setprecision(2) << gflops << " GFLOPS" << std::endl;

        // 计算有效内存带宽
        double memory_bytes = (nonzeros * sizeof(float)) +           // A values
                             (nonzeros * sizeof(int)) +             // A column indices
                             ((M + 1) * sizeof(int)) +              // A row offsets
                             (K * N * sizeof(float)) +               // B matrix
                             (M * N * sizeof(float));                // C matrix
        double bandwidth_gb_s = (memory_bytes / (time_ms * 1e-3)) / 1e9;
        std::cout << "内存带宽: " << std::fixed << std::setprecision(2) << bandwidth_gb_s << " GB/s" << std::endl;
        std::cout << "=============================================" << std::endl;
    }
};

int main(int argc, char* argv[]) {
    // 解析命令行参数
    int M = 1024, N = 512, K = 1024;  // 适中的测试大小
    float sparsity = 0.9f;  // 90%稀疏度

    if (argc >= 4) {
        M = std::atoi(argv[1]);
        N = std::atoi(argv[2]);
        K = std::atoi(argv[3]);
    }
    if (argc >= 5) {
        sparsity = std::atof(argv[4]);
    }

    std::cout << "Sputnik SPMM 测试程序" << std::endl;
    std::cout << "使用参数: M=" << M << ", N=" << N << ", K=" << K << ", 稀疏度=" << sparsity << std::endl;

    try {
        SputnikSpmmTest test(M, N, K, sparsity);

        // 先进行功能测试
        if (test.runSputnikTest()) {
            std::cout << "\n✓ 功能测试通过，开始性能测试..." << std::endl;
            test.runBenchmark();
        } else {
            std::cerr << "\n✗ 功能测试失败，跳过性能测试" << std::endl;
            return 1;
        }

    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}
