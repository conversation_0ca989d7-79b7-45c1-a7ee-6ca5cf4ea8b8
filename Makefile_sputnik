# Sputnik SPMM 测试 Makefile

# 编译器设置
NVCC = /usr/local/cuda/bin/nvcc
CXX = g++

# CUDA架构设置
CUDA_ARCH = -gencode arch=compute_86,code=sm_86

# Sputnik路径 (基于项目实际路径)
SPUTNIK_ROOT = SpInfer/third_party/sputnik
SPUTNIK_INCLUDE = -I$(SPUTNIK_ROOT)
SPUTNIK_LIB_DIR = $(SPUTNIK_ROOT)/build/sputnik
SPUTNIK_LIB = -L$(SPUTNIK_LIB_DIR) -lsputnik

# 编译标志
NVCC_FLAGS = -ccbin $(CXX) \
             -I/usr/local/cuda/include/ \
             $(SPUTNIK_INCLUDE) \
             -m64 \
             -Xcompiler -fPIC \
             --threads 0 \
             --std=c++11 \
             -maxrregcount=255 \
             --use_fast_math \
             --ptxas-options=-v,-warn-lmem-usage,--warn-on-spills \
             $(CUDA_ARCH)

# 链接库
LIBS = -lcudart $(SPUTNIK_LIB)

# 目标文件
TARGET = sputnik_test
TARGET_SIMPLE = sputnik_simple_test
SOURCE = sputnik_test.cu
SOURCE_SIMPLE = sputnik_simple_test.cu

# 默认目标
all: check_sputnik $(TARGET)

# 简化版本目标
simple: check_sputnik $(TARGET_SIMPLE)

# 检查Sputnik是否存在并已编译
check_sputnik:
	@if [ ! -d "$(SPUTNIK_ROOT)" ]; then \
		echo "错误: 未找到Sputnik库目录: $(SPUTNIK_ROOT)"; \
		exit 1; \
	fi
	@if [ ! -f "$(SPUTNIK_LIB_DIR)/libsputnik.so" ]; then \
		echo "错误: 未找到编译好的Sputnik库: $(SPUTNIK_LIB_DIR)/libsputnik.so"; \
		echo "请先编译Sputnik库:"; \
		echo "  cd $(SPUTNIK_ROOT)/build && make -j8"; \
		exit 1; \
	fi
	@echo "✓ Sputnik库检查通过"

# 编译规则
$(TARGET): $(SOURCE)
	@echo "编译 Sputnik SPMM 测试..."
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE) $(LIBS)
	@echo "编译完成: $(TARGET)"

# 简化版本编译规则
$(TARGET_SIMPLE): $(SOURCE_SIMPLE)
	@echo "编译简化版 Sputnik SPMM 测试..."
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET_SIMPLE) $(SOURCE_SIMPLE) $(LIBS)
	@echo "编译完成: $(TARGET_SIMPLE)"

# 运行测试 (默认参数)
run: $(TARGET)
	@echo "运行 Sputnik SPMM 测试 (默认参数)..."
	LD_LIBRARY_PATH=$(SPUTNIK_LIB_DIR):$$LD_LIBRARY_PATH ./$(TARGET)

# 运行测试 (自定义参数)
run_custom: $(TARGET)
	@echo "运行 Sputnik SPMM 测试 (M=$(M) N=$(N) K=$(K) 稀疏度=$(SPARSITY))..."
	LD_LIBRARY_PATH=$(SPUTNIK_LIB_DIR):$$LD_LIBRARY_PATH ./$(TARGET) $(M) $(N) $(K) $(SPARSITY)

# 运行简化版本测试
run_simple: $(TARGET_SIMPLE)
	@echo "运行简化版 Sputnik SPMM 测试..."
	LD_LIBRARY_PATH=$(SPUTNIK_LIB_DIR):$$LD_LIBRARY_PATH ./$(TARGET_SIMPLE)

# 运行简化版本测试 (自定义参数)
run_simple_custom: $(TARGET_SIMPLE)
	@echo "运行简化版 Sputnik SPMM 测试 (M=$(M) N=$(N) K=$(K) 稀疏度=$(SPARSITY))..."
	LD_LIBRARY_PATH=$(SPUTNIK_LIB_DIR):$$LD_LIBRARY_PATH ./$(TARGET_SIMPLE) $(M) $(N) $(K) $(SPARSITY)

# 编译Sputnik库 (如果需要)
build_sputnik:
	@echo "编译 Sputnik 库..."
	@if [ ! -d "$(SPUTNIK_ROOT)/build" ]; then \
		echo "创建build目录..."; \
		mkdir -p $(SPUTNIK_ROOT)/build; \
	fi
	cd $(SPUTNIK_ROOT)/build && \
		cmake .. -DCMAKE_BUILD_TYPE=Release -DCUDA_ARCHS="86" && \
		make -j8
	@echo "Sputnik 库编译完成!"

# 清理
clean:
	@echo "清理编译文件..."
	rm -f $(TARGET) $(TARGET_SIMPLE)

# 完全清理 (包括Sputnik build)
clean_all: clean
	@echo "清理所有文件..."
	rm -rf $(SPUTNIK_ROOT)/build

# 帮助信息
help:
	@echo "Sputnik SPMM 测试 Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all           - 编译程序 (需要Sputnik库)"
	@echo "  simple        - 编译简化版本"
	@echo "  run           - 运行测试 (默认参数)"
	@echo "  run_custom    - 运行测试 (自定义参数)"
	@echo "  run_simple    - 运行简化版本测试"
	@echo "  run_simple_custom - 运行简化版本测试 (自定义参数)"
	@echo "  build_sputnik - 编译Sputnik库"
	@echo "  clean         - 清理编译文件"
	@echo "  clean_all     - 清理所有文件"
	@echo "  help          - 显示此帮助信息"
	@echo ""
	@echo "自定义参数示例:"
	@echo "  make run_custom M=8192 N=4096 K=64 SPARSITY=0.8"
	@echo ""
	@echo "GPU指定示例:"
	@echo "  CUDA_VISIBLE_DEVICES=3 make run"

# 声明伪目标
.PHONY: all run run_custom build_sputnik clean clean_all help check_sputnik
