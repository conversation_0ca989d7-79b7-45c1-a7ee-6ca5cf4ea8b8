# cuBLAS GEMM Benchmark Makefile
# 与SpInfer保持一致的编译配置

# 编译器设置
NVCC = /usr/local/cuda/bin/nvcc
CXX = g++

# CUDA架构设置 (与SpInfer一致)
CUDA_ARCH = -gencode arch=compute_86,code=sm_86

# 编译标志
NVCC_FLAGS = -ccbin $(CXX) \
             -I/usr/local/cuda/include/ \
             -m64 \
             -Xcompiler -fPIC \
             --threads 0 \
             --std=c++11 \
             -maxrregcount=255 \
             --use_fast_math \
             --ptxas-options=-v,-warn-lmem-usage,--warn-on-spills \
             $(CUDA_ARCH)

# 链接库
LIBS = -lcublas -lcudart

# 目标文件
TARGET = cublas_gemm_benchmark
SOURCE = cublas_gemm_benchmark.cu

# 默认目标
all: $(TARGET)

# 编译规则
$(TARGET): $(SOURCE)
	@echo "编译 cuBLAS GEMM 基准测试..."
	$(NVCC) $(NVCC_FLAGS) -o $(TARGET) $(SOURCE) $(LIBS)
	@echo "编译完成: $(TARGET)"

# 运行测试 (默认参数: 18944 x 3584 x 32)
run: $(TARGET)
	@echo "运行 cuBLAS GEMM 基准测试 (默认参数)..."
	./$(TARGET)

# 运行测试 (自定义参数)
run_custom: $(TARGET)
	@echo "运行 cuBLAS GEMM 基准测试 (自定义参数: M=$(M) N=$(N) K=$(K))..."
	./$(TARGET) $(M) $(N) $(K)

# 运行与SpInfer相同的参数
run_spinfer: $(TARGET)
	@echo "运行 cuBLAS GEMM 基准测试 (SpInfer参数: 18944 x 3584 x 32)..."
	./$(TARGET) 18944 3584 32

# 性能分析 (使用nsys)
profile: $(TARGET)
	@echo "使用 nsys 进行性能分析..."
	@mkdir -p profiles
	nsys profile \
		--output=profiles/cublas_gemm_profile \
		--force-overwrite=true \
		--trace=cuda,nvtx,cublas \
		./$(TARGET)

# 性能分析 (使用ncu)
profile_ncu: $(TARGET)
	@echo "使用 ncu 进行详细性能分析..."
	@mkdir -p profiles
	ncu --set full \
		--target-processes all \
		--export profiles/cublas_gemm_ncu_analysis \
		--force-overwrite \
		./$(TARGET)

# 清理
clean:
	@echo "清理编译文件..."
	rm -f $(TARGET)
	rm -rf profiles

# 帮助信息
help:
	@echo "cuBLAS GEMM Benchmark Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all          - 编译程序"
	@echo "  run          - 运行测试 (默认参数: 18944 x 3584 x 32)"
	@echo "  run_custom   - 运行测试 (自定义参数: make run_custom M=<m> N=<n> K=<k>)"
	@echo "  run_spinfer  - 运行测试 (SpInfer参数)"
	@echo "  profile      - 使用 nsys 进行性能分析"
	@echo "  profile_ncu  - 使用 ncu 进行详细性能分析"
	@echo "  clean        - 清理编译文件"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "示例:"
	@echo "  make run                              # 默认参数"
	@echo "  make run_custom M=8192 N=4096 K=64   # 自定义参数"
	@echo "  CUDA_VISIBLE_DEVICES=2 make run      # 指定GPU"

# 声明伪目标
.PHONY: all run run_custom run_spinfer profile profile_ncu clean help
