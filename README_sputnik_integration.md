# Sputnik集成指南

## 概述

本文档介绍如何将Google Research的Sputnik库集成到你的混合kernel项目中，作为CUDA Core (CC) kernel的实现。

## Sputnik项目分析

### 1. Sputnik简介
- **开发者**: Google Research
- **用途**: 深度学习中的稀疏线性代数运算
- **特点**: 专门针对CUDA Core优化的稀疏矩阵乘法
- **格式**: 使用CSR (Compressed Sparse Row) 格式
- **精度**: 支持FP16和FP32

### 2. API接口

#### 主要函数
```cpp
// FP32版本
cudaError_t CudaSpmm(int m, int k, int n, int nonzeros,
                     const int* __restrict__ row_indices,
                     const float* __restrict__ values,
                     const int* __restrict__ row_offsets,
                     const int* __restrict__ column_indices,
                     const float* __restrict__ dense_matrix,
                     float* __restrict__ output_matrix,
                     cudaStream_t stream);

// FP16版本 (使用half2和short2)
cudaError_t CudaSpmm(int m, int k, int n, int nonzeros,
                     const int* __restrict__ row_indices,
                     const half2* __restrict__ values,
                     const int* __restrict__ row_offsets,
                     const short2* __restrict__ column_indices,
                     const half2* __restrict__ dense_matrix,
                     half2* __restrict__ output_matrix,
                     cudaStream_t stream);
```

#### 参数说明
- `m`: 稀疏矩阵A的行数
- `k`: 稀疏矩阵A的列数，密集矩阵B的行数
- `n`: 密集矩阵B的列数
- `nonzeros`: 稀疏矩阵A中非零元素的数量
- `row_indices`: 行索引重排序数组 (用于负载均衡)
- `values`: 非零元素值数组
- `row_offsets`: CSR格式的行偏移数组 (大小为m+1)
- `column_indices`: CSR格式的列索引数组
- `dense_matrix`: 密集矩阵B (行主序)
- `output_matrix`: 输出矩阵C (行主序)
- `stream`: CUDA流

### 3. 数据格式要求

#### CSR格式
```
稀疏矩阵A:
[1.0  0   2.0  0  ]
[0    3.0  0   4.0]
[5.0  0    0   6.0]

CSR表示:
values = [1.0, 2.0, 3.0, 4.0, 5.0, 6.0]
column_indices = [0, 2, 1, 3, 0, 3]
row_offsets = [0, 2, 4, 6]
```

#### 行重排序
Sputnik需要`row_indices`数组来重新排列行，以实现更好的负载均衡。简单情况下可以使用顺序索引：
```cpp
for (int i = 0; i < m; i++) {
    row_indices[i] = i;
}
```

## 集成方案

### 1. 项目结构
```
你的项目/
├── SpInfer/
│   ├── third_party/
│   │   └── sputnik/          # 已有的Sputnik库
│   │       ├── build/
│   │       │   └── sputnik/
│   │       │       └── libsputnik.so
│   │       └── sputnik/
│   │           └── spmm/
│   │               └── cuda_spmm.h
│   └── csrc/                 # SpInfer源码
├── sputnik_test.cu           # 单独的Sputnik测试
├── hybrid_sputnik_test.cu    # 混合kernel测试
├── Makefile_sputnik          # 编译配置
└── README_sputnik_integration.md
```

### 2. 编译配置

#### Makefile设置
```makefile
# Sputnik路径
SPUTNIK_ROOT = SpInfer/third_party/sputnik
SPUTNIK_INCLUDE = -I$(SPUTNIK_ROOT)
SPUTNIK_LIB = -L$(SPUTNIK_ROOT)/build/sputnik -lsputnik

# 编译标志
NVCC_FLAGS = -I$(SPUTNIK_ROOT) --std=c++11 $(CUDA_ARCH)
LIBS = -lcudart $(SPUTNIK_LIB)
```

#### 运行时库路径
```bash
export LD_LIBRARY_PATH=SpInfer/third_party/sputnik/build/sputnik:$LD_LIBRARY_PATH
```

### 3. 使用示例

#### 基本使用
```cpp
#include "SpInfer/third_party/sputnik/sputnik/spmm/cuda_spmm.h"

// 调用Sputnik SPMM
cudaError_t result = sputnik::CudaSpmm(
    m, k, n, nonzeros,
    row_indices_gpu,
    values_gpu,
    row_offsets_gpu,
    column_indices_gpu,
    dense_matrix_gpu,
    output_matrix_gpu,
    stream
);
```

#### 混合kernel集成
```cpp
// 并行执行TC和CC kernel
cudaStream_t stream_tc, stream_cc;
cudaStreamCreateWithFlags(&stream_tc, cudaStreamNonBlocking);
cudaStreamCreateWithFlags(&stream_cc, cudaStreamNonBlocking);

// Matrix1: SpInfer TC kernel
SpMM_API_Matrix1(..., stream_tc);

// Matrix2: Sputnik CC kernel  
sputnik::CudaSpmm(matrix2_m, k, n, matrix2_nonzeros,
                  matrix2_row_indices, matrix2_values,
                  matrix2_row_offsets, matrix2_column_indices,
                  dense_matrix, matrix2_output, stream_cc);

// 同步
cudaStreamSynchronize(stream_tc);
cudaStreamSynchronize(stream_cc);
```

## 测试程序

### 1. 单独测试Sputnik
```bash
# 编译
make -f Makefile_sputnik all

# 运行默认测试
make -f Makefile_sputnik run

# 自定义参数测试
make -f Makefile_sputnik run_custom M=8192 N=4096 K=64 SPARSITY=0.8
```

### 2. 混合kernel测试
```bash
# 编译混合测试
nvcc -I SpInfer/third_party/sputnik -L SpInfer/third_party/sputnik/build/sputnik \
     -o hybrid_test hybrid_sputnik_test.cu -lcudart -lsputnik

# 运行
LD_LIBRARY_PATH=SpInfer/third_party/sputnik/build/sputnik:$LD_LIBRARY_PATH \
./hybrid_test 18944 3584 32
```

## 性能优化建议

### 1. 数据格式优化
- 对于Matrix2 (CC路径)，确保数据符合Sputnik的CSR格式要求
- 考虑使用FP16版本以提高内存带宽利用率
- 优化行重排序以提高负载均衡

### 2. 内存管理
- 使用统一内存或预分配GPU内存
- 避免频繁的CPU-GPU数据传输
- 考虑使用CUDA内存池

### 3. 并行执行
- 使用非阻塞CUDA流实现真正的并行执行
- 确保TC和CC路径的资源不冲突
- 优化kernel启动开销

## 已知限制

### 1. 数据格式转换
- SpInfer使用自定义格式，需要转换为CSR格式
- FP16版本需要使用half2和short2，要求数据对齐

### 2. 内存开销
- 需要额外存储CSR格式数据
- 可能需要维护多份数据副本

### 3. 同步开销
- 混合执行需要流同步
- 结果合并可能成为瓶颈

## 下一步工作

1. **完善数据转换**: 实现SpInfer格式到CSR格式的高效转换
2. **优化内存使用**: 减少数据冗余，优化内存布局
3. **性能调优**: 对比不同配置的性能表现
4. **结果验证**: 确保计算结果的正确性
5. **集成测试**: 与现有的SpInfer系统完全集成

## 参考资料

- [Sputnik GitHub仓库](https://github.com/google-research/sputnik)
- [CSR格式说明](https://en.wikipedia.org/wiki/Sparse_matrix#Compressed_sparse_row_(CSR,_CRS_or_Yale_format))
- [CUDA编程指南](https://docs.nvidia.com/cuda/cuda-c-programming-guide/)
