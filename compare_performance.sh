#!/bin/bash

# SpInfer vs cuBLAS 性能对比脚本

echo "=========================================="
echo "SpInfer vs cuBLAS 性能对比测试"
echo "=========================================="

# 设置默认参数
M=${1:-18944}
N=${2:-3584}
K=${3:-32}

echo "测试参数: M=$M, N=$N, K=$K"
echo ""

# 检查CUDA设备
if [ -z "$CUDA_VISIBLE_DEVICES" ]; then
    echo "警告: 未设置 CUDA_VISIBLE_DEVICES，使用默认GPU3"
    export CUDA_VISIBLE_DEVICES=3
fi

echo "使用GPU: $CUDA_VISIBLE_DEVICES"
echo ""

# 编译程序
echo "========== 编译程序 =========="
echo "编译 cuBLAS 基准测试..."
make -f Makefile_cublas clean > /dev/null 2>&1
make -f Makefile_cublas all

if [ $? -ne 0 ]; then
    echo "错误: cuBLAS 编译失败"
    exit 1
fi

echo "编译 SpInfer 测试程序..."
cd SpInfer
make -f Makefile_three_kernels clean > /dev/null 2>&1
make -f Makefile_three_kernels test_dual_kernels

if [ $? -ne 0 ]; then
    echo "错误: SpInfer 编译失败"
    exit 1
fi
cd ..

echo "编译完成!"
echo ""

# 运行cuBLAS基准测试
echo "========== cuBLAS 基准测试 =========="
./cublas_gemm_benchmark $M $N $K
echo ""

# 运行SpInfer测试
echo "========== SpInfer 测试 =========="
cd SpInfer
./test_dual_kernels
cd ..
echo ""

# 总结
echo "=========================================="
echo "性能对比完成!"
echo ""
echo "说明:"
echo "- cuBLAS: 使用 Tensor Core 的密集矩阵乘法"
echo "- SpInfer: 稀疏矩阵乘法 (TC + CC 混合路径)"
echo ""
echo "注意事项:"
echo "1. cuBLAS 处理的是密集矩阵，理论性能上限"
echo "2. SpInfer 处理的是稀疏矩阵，实际应用场景"
echo "3. 性能对比应考虑稀疏度和计算复杂度差异"
echo "=========================================="
