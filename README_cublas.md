# cuBLAS GEMM 基准测试

这个程序提供了一个使用 Tensor Core 的 cuBLAS GEMM 基准测试，用于与 SpInfer 稀疏矩阵乘法进行性能对比。

## 功能特性

- **Tensor Core 加速**: 使用 `CUBLAS_TENSOR_OP_MATH` 模式
- **FP16 精度**: 与 SpInfer 保持一致的数据类型
- **内存布局兼容**: 输入数据 A、B、C 与 SpInfer 顺序一致
- **详细性能指标**: 输出时间、TFLOPS、内存带宽等指标
- **灵活参数**: 支持自定义矩阵大小

## 编译和运行

### 基本使用

```bash
# 编译
make -f Makefile_cublas

# 运行 (默认参数: 18944 x 3584 x 32)
make -f Makefile_cublas run

# 运行 (自定义参数)
make -f Makefile_cublas run_custom M=8192 N=4096 K=64

# 运行 (SpInfer 相同参数)
make -f Makefile_cublas run_spinfer
```

### 指定GPU

```bash
CUDA_VISIBLE_DEVICES=2 make -f Makefile_cublas run
```

### 性能分析

```bash
# 使用 nsys 分析
make -f Makefile_cublas profile

# 使用 ncu 分析
make -f Makefile_cublas profile_ncu
```

## 与 SpInfer 对比

### 自动对比脚本

```bash
# 使用默认参数对比
./compare_performance.sh

# 使用自定义参数对比
./compare_performance.sh 8192 4096 64

# 指定GPU对比
CUDA_VISIBLE_DEVICES=2 ./compare_performance.sh
```

### 手动对比

```bash
# 1. 运行 cuBLAS 基准测试
./cublas_gemm_benchmark 18944 3584 32

# 2. 运行 SpInfer 测试
cd SpInfer
./test_dual_kernels
```

## 输出说明

### cuBLAS 输出示例

```
========== cuBLAS GEMM 性能测试结果 ==========
矩阵大小: 18944 x 32 x 3584
迭代次数: 100
平均时间: 0.125 ms
性能:     345.67 TFLOPS
内存带宽: 1234.56 GB/s
=============================================
```

### 性能指标说明

- **平均时间**: 单次 GEMM 操作的平均执行时间
- **TFLOPS**: 每秒万亿次浮点运算 (2×M×N×K FLOP)
- **内存带宽**: 有效内存带宽 (考虑 A、B、C 矩阵的数据传输)

## 技术细节

### 矩阵布局

- **A 矩阵**: M × K (行主序)
- **B 矩阵**: K × N (行主序)  
- **C 矩阵**: M × N (行主序)

### cuBLAS 调用

```cpp
cublasHgemm(handle,
           CUBLAS_OP_N, CUBLAS_OP_N,  // 不转置
           N, M, K,                   // cuBLAS 列主序约定
           &alpha,
           B_gpu, N,                  // B: K×N, ldb=N
           A_gpu, K,                  // A: M×K, lda=K
           &beta,
           C_gpu, N);                 // C: M×N, ldc=N
```

### Tensor Core 要求

- 矩阵维度必须是 8 的倍数 (FP16)
- 内存对齐要求 (128-bit 对齐)
- 使用 `CUBLAS_TENSOR_OP_MATH` 模式

## 性能对比分析

### 理论性能上限

cuBLAS 代表了密集矩阵乘法的理论性能上限，可以作为 SpInfer 稀疏矩阵乘法的参考基准。

### 对比维度

1. **绝对性能**: 执行时间对比
2. **相对效率**: SpInfer 相对于 cuBLAS 的效率比
3. **资源利用**: GPU 利用率、内存带宽等
4. **稀疏度影响**: 不同稀疏度下的性能变化

### 预期结果

- **cuBLAS**: 密集计算，高 TFLOPS，高内存带宽
- **SpInfer**: 稀疏计算，较低 TFLOPS，但考虑稀疏度后的有效计算效率

## 故障排除

### 编译错误

```bash
# 检查 CUDA 安装
nvcc --version

# 检查 cuBLAS 库
ls /usr/local/cuda/lib64/libcublas*
```

### 运行时错误

```bash
# 检查 GPU 可用性
nvidia-smi

# 检查 CUDA 设备
./cublas_gemm_benchmark 100 100 100  # 小矩阵测试
```

### 性能异常

- 确保 GPU 处于性能模式
- 检查 GPU 温度和频率
- 验证 Tensor Core 是否被正确使用

## 扩展功能

可以根据需要扩展以下功能：

1. **多精度支持**: FP32、INT8 等
2. **批处理 GEMM**: 多个矩阵并行计算
3. **内存池**: 减少内存分配开销
4. **自动调优**: 不同参数下的最优配置
