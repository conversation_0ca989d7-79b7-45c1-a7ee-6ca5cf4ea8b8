import torch
from triton.ops.blocksparse.matmul import matmul

BATCH, HEADS, M, K, N, BLOCK = 1, 1, 18944, 3584, 32, 32
device = "cuda:0"

A = torch.randn((BATCH, HEADS, M, K), device=device, dtype=torch.float16)
B_ = torch.randn((BATCH, HEADS, K, N), device=device, dtype=torch.float16)

# layout: (HEADS, M//BLOCK, K//BLOCK) = (1, 592, 112)
layout = torch.randint(
    0, 2, (HEADS, M // BLOCK, K // BLOCK),
    dtype=torch.int32, device=device
).to(torch.bool)

print("layout.shape =", layout.shape)  # (1, 592, 112)

bsmm = matmul(layout=layout, block=BLOCK, device=device, mode="sdd")

C = bsmm(A, B_)
print("Output shape:", C.shape)  # 应该是 (1, 1, 18944, 32)
