#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <cuda_fp16.h>
#include <iostream>
#include <iomanip>

// 错误检查宏
#define CHECK_CUDA(call) do { \
    cudaError_t err = call; \
    if (err != cudaSuccess) { \
        std::cerr << "CUDA error: " << cudaGetErrorString(err) << std::endl; \
        exit(1); \
    } \
} while(0)

#define CHECK_CUBLAS(call) do { \
    cublasStatus_t status = call; \
    if (status != CUBLAS_STATUS_SUCCESS) { \
        std::cerr << "cuBLAS error: " << status << std::endl; \
        exit(1); \
    } \
} while(0)

int main(int argc, char* argv[]) {
    // 解析命令行参数
    int M = 18944, N = 3584, K = 32;  // 默认值
    
    if (argc >= 4) {
        M = std::atoi(argv[1]);
        N = std::atoi(argv[2]);
        K = std::atoi(argv[3]);
    }
    
    std::cout << "cuBLAS GEMM Tensor Core 测试" << std::endl;
    std::cout << "矩阵大小: A(" << M << "x" << K << ") × B(" << K << "x" << N << ") = C(" << M << "x" << N << ")" << std::endl;
    
    // 初始化cuBLAS
    cublasHandle_t handle;
    CHECK_CUBLAS(cublasCreate(&handle));
    CHECK_CUBLAS(cublasSetMathMode(handle, CUBLAS_TENSOR_OP_MATH));
    
    // 分配GPU内存
    half *A_gpu, *B_gpu, *C_gpu;
    CHECK_CUDA(cudaMalloc(&A_gpu, M * K * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&B_gpu, K * N * sizeof(half)));
    CHECK_CUDA(cudaMalloc(&C_gpu, M * N * sizeof(half)));
    
    // 初始化数据
    std::cout << "初始化数据..." << std::endl;
    
    // 在GPU上直接初始化为简单值
    CHECK_CUDA(cudaMemset(A_gpu, 0, M * K * sizeof(half)));
    CHECK_CUDA(cudaMemset(B_gpu, 0, K * N * sizeof(half)));
    CHECK_CUDA(cudaMemset(C_gpu, 0, M * N * sizeof(half)));
    
    // 设置一些非零值用于测试
    half h_one = __float2half(1.0f);
    for (int i = 0; i < std::min(1000, M * K); i += 100) {
        CHECK_CUDA(cudaMemcpy(A_gpu + i, &h_one, sizeof(half), cudaMemcpyHostToDevice));
    }
    for (int i = 0; i < std::min(1000, K * N); i += 100) {
        CHECK_CUDA(cudaMemcpy(B_gpu + i, &h_one, sizeof(half), cudaMemcpyHostToDevice));
    }
    
    // 创建CUDA事件用于计时
    cudaEvent_t start, stop;
    CHECK_CUDA(cudaEventCreate(&start));
    CHECK_CUDA(cudaEventCreate(&stop));
    
    // GEMM参数
    const half alpha = __float2half(1.0f);
    const half beta = __float2half(0.0f);
    
    // 预热
    std::cout << "预热..." << std::endl;
    for (int i = 0; i < 10; i++) {
        CHECK_CUBLAS(cublasHgemm(handle,
                               CUBLAS_OP_N, CUBLAS_OP_N,
                               N, M, K,
                               &alpha,
                               B_gpu, N,
                               A_gpu, K,
                               &beta,
                               C_gpu, N));
    }
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // 性能测试
    std::cout << "开始性能测试..." << std::endl;
    const int iterations = 100;
    
    CHECK_CUDA(cudaEventRecord(start));
    for (int i = 0; i < iterations; i++) {
        CHECK_CUBLAS(cublasHgemm(handle,
                               CUBLAS_OP_N, CUBLAS_OP_N,
                               N, M, K,
                               &alpha,
                               B_gpu, N,
                               A_gpu, K,
                               &beta,
                               C_gpu, N));
    }
    CHECK_CUDA(cudaEventRecord(stop));
    CHECK_CUDA(cudaEventSynchronize(stop));
    
    // 计算性能
    float elapsed_time;
    CHECK_CUDA(cudaEventElapsedTime(&elapsed_time, start, stop));
    
    float avg_time = elapsed_time / iterations;
    double flops = 2.0 * M * N * K;
    double tflops = (flops / (avg_time * 1e-3)) / 1e12;
    
    // 计算内存带宽
    double memory_bytes = (static_cast<double>(M * K) + K * N + M * N) * sizeof(half);
    double bandwidth_gb_s = (memory_bytes / (avg_time * 1e-3)) / 1e9;
    
    // 输出结果
    std::cout << "\n========== cuBLAS GEMM 性能测试结果 ==========" << std::endl;
    std::cout << "矩阵大小: " << M << " x " << K << " x " << N << std::endl;
    std::cout << "迭代次数: " << iterations << std::endl;
    std::cout << "平均时间: " << std::fixed << std::setprecision(3) << avg_time << " ms" << std::endl;
    std::cout << "性能:     " << std::fixed << std::setprecision(2) << tflops << " TFLOPS" << std::endl;
    std::cout << "内存带宽: " << std::fixed << std::setprecision(2) << bandwidth_gb_s << " GB/s" << std::endl;
    std::cout << "=============================================" << std::endl;
    
    // 清理资源
    cudaFree(A_gpu);
    cudaFree(B_gpu);
    cudaFree(C_gpu);
    cudaEventDestroy(start);
    cudaEventDestroy(stop);
    cublasDestroy(handle);
    
    return 0;
}
